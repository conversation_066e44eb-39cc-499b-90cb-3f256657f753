"""
消息处理器模块
"""
import json
import time
import random
from typing import Dict, Any, Optional, Callable
from httpAPI import LLOneBotClient
from ai_interface import KimiA<PERSON>
from utils import HumanLikeDelay, MessageParser, ResponseF<PERSON>atter, SafetyChecker, logger
from config import Config

class MessageHandler:
    """消息处理器"""
    
    def __init__(self, client: LLOneBotClient, ai: KimiAI = None):
        self.client = client
        self.ai = ai or KimiAI()
        self.bot_qq = Config.BOT_QQ
        
        # 消息处理回调
        self.message_callbacks = []
        
        logger.info("消息处理器初始化完成")
    
    def add_message_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """添加消息处理回调函数"""
        self.message_callbacks.append(callback)
    
    def should_smart_reply(self, group_id: str, message: str, user_id: str) -> bool:
        """
        判断是否应该智能回复

        Args:
            group_id: 群号
            message: 消息内容
            user_id: 用户ID

        Returns:
            是否应该回复
        """
        # 检查是否启用智能回复
        if not Config.ENABLE_SMART_REPLY:
            return False

        # 检查是否在智能回复群列表中
        if group_id not in Config.SMART_REPLY_GROUPS:
            return False

        # 过滤掉机器人自己的消息
        if user_id == self.bot_qq:
            return False

        # 清理消息内容
        clean_msg = MessageParser.clean_message(message)

        # 消息长度检查
        if len(clean_msg) < Config.SMART_REPLY_MIN_LENGTH:
            return False

        # 概率判断
        if random.random() > Config.SMART_REPLY_PROBABILITY:
            return False

        # 特殊触发词（提高回复概率）
        trigger_words = ["问题", "怎么", "为什么", "帮助", "求助", "？", "?"]
        if any(word in clean_msg for word in trigger_words):
            return True

        # 如果消息很长，降低回复概率
        if len(clean_msg) > 50:
            return random.random() < 0.1

        return True

    def handle_group_message(self, message_data: Dict[str, Any]) -> bool:
        """
        处理群消息

        Args:
            message_data: 消息数据

        Returns:
            是否处理成功
        """
        try:
            # 提取消息信息
            group_id = str(message_data.get("group_id", ""))
            user_id = str(message_data.get("user_id", ""))
            message = message_data.get("message", "")
            message_id = message_data.get("message_id", "")

            logger.info(f"收到群消息 - 群:{group_id}, 用户:{user_id}, 内容:{message[:50]}...")

            # 安全检查
            if not SafetyChecker.is_safe_message(message):
                logger.warning(f"检测到不安全消息，拒绝处理: {message}")
                return False

            # 检查回复条件
            should_reply = False
            reply_type = ""

            # 1. 检查是否@了机器人
            if Config.ENABLE_AT_REPLY and MessageParser.is_at_bot(message, self.bot_qq):
                should_reply = True
                reply_type = "@回复"

            # 2. 检查是否应该智能回复（仅当未被@时）
            elif self.should_smart_reply(group_id, message, user_id):
                should_reply = True
                reply_type = "智能回复"

            if not should_reply:
                logger.debug(f"群消息不满足回复条件，跳过处理")
                return False

            logger.info(f"触发{reply_type} - 群:{group_id}")

            # 人性化延迟
            HumanLikeDelay.random_delay()

            # 获取AI回复
            logger.info("正在生成AI回复...")
            ai_reply = self.ai.get_qq_reply(message, user_id, group_id)

            if not ai_reply:
                logger.error("AI回复生成失败")
                ai_reply = ResponseFormatter.format_error_reply()

            # 格式化回复
            if reply_type == "@回复":
                # @回复时需要@用户
                reply_message = ResponseFormatter.format_at_reply(user_id, ai_reply)
            else:
                # 智能回复时直接回复，不@用户
                reply_message = ai_reply

            # 模拟打字延迟
            HumanLikeDelay.typing_delay(ai_reply)

            # 发送回复
            result = self.client.send_group_msg(group_id, reply_message)

            if result.get("status") == "ok":
                logger.info(f"{reply_type}成功 - 群:{group_id}, 回复:{ai_reply[:50]}...")
                return True
            else:
                logger.error(f"{reply_type}失败: {result}")
                return False

        except Exception as e:
            logger.error(f"处理群消息异常: {e}")
            return False
    
    def handle_private_message(self, message_data: Dict[str, Any]) -> bool:
        """
        处理私聊消息

        Args:
            message_data: 消息数据

        Returns:
            是否处理成功
        """
        try:
            user_id = str(message_data.get("user_id", ""))
            message = message_data.get("message", "")

            logger.info(f"收到私聊消息 - 用户:{user_id}, 内容:{message[:50]}...")

            # 检查是否启用私聊回复
            if not Config.ENABLE_PRIVATE_REPLY:
                logger.debug("私聊回复功能已关闭，跳过处理")
                return False

            # 过滤掉机器人自己的消息
            if user_id == self.bot_qq:
                logger.debug("跳过机器人自己的消息")
                return False

            # 安全检查
            if not SafetyChecker.is_safe_message(message):
                logger.warning(f"检测到不安全消息，拒绝处理: {message}")
                return False

            # 人性化延迟
            HumanLikeDelay.random_delay()

            # 获取AI回复
            logger.info("正在生成私聊AI回复...")
            ai_reply = self.ai.get_qq_reply(message, user_id)

            if not ai_reply:
                logger.error("私聊AI回复生成失败")
                ai_reply = ResponseFormatter.format_error_reply()

            # 模拟打字延迟
            HumanLikeDelay.typing_delay(ai_reply)

            # 发送回复
            result = self.client.send_private_msg(user_id, ai_reply)

            if result.get("status") == "ok":
                logger.info(f"私聊回复成功 - 用户:{user_id}, 回复:{ai_reply[:50]}...")
                return True
            else:
                logger.error(f"私聊回复失败: {result}")
                return False

        except Exception as e:
            logger.error(f"处理私聊消息异常: {e}")
            return False
    
    def process_message(self, message_data: Dict[str, Any]) -> bool:
        """
        处理消息的统一入口
        
        Args:
            message_data: 消息数据
            
        Returns:
            是否处理成功
        """
        try:
            message_type = message_data.get("message_type", "")
            
            # 调用自定义回调
            for callback in self.message_callbacks:
                try:
                    callback(message_data)
                except Exception as e:
                    logger.error(f"消息回调执行异常: {e}")
            
            # 根据消息类型处理
            if message_type == "group":
                return self.handle_group_message(message_data)
            elif message_type == "private":
                return self.handle_private_message(message_data)
            else:
                logger.debug(f"未知消息类型: {message_type}")
                return False
                
        except Exception as e:
            logger.error(f"处理消息异常: {e}")
            return False

"""
消息处理器模块
"""
import json
import time
from typing import Dict, Any, Optional, Callable
from httpAPI import LLOneBotClient
from ai_interface import KimiAI
from utils import HumanLikeDelay, MessageParser, ResponseFormatter, SafetyChecker, logger
from config import Config

class MessageHandler:
    """消息处理器"""
    
    def __init__(self, client: LLOneBotClient, ai: KimiAI = None):
        self.client = client
        self.ai = ai or KimiAI()
        self.bot_qq = Config.BOT_QQ
        
        # 消息处理回调
        self.message_callbacks = []
        
        logger.info("消息处理器初始化完成")
    
    def add_message_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """添加消息处理回调函数"""
        self.message_callbacks.append(callback)
    
    def handle_group_message(self, message_data: Dict[str, Any]) -> bool:
        """
        处理群消息
        
        Args:
            message_data: 消息数据
            
        Returns:
            是否处理成功
        """
        try:
            # 提取消息信息
            group_id = str(message_data.get("group_id", ""))
            user_id = str(message_data.get("user_id", ""))
            message = message_data.get("message", "")
            message_id = message_data.get("message_id", "")
            
            logger.info(f"收到群消息 - 群:{group_id}, 用户:{user_id}, 内容:{message[:50]}...")
            
            # 检查是否@了机器人
            if not MessageParser.is_at_bot(message, self.bot_qq):
                logger.debug("消息未@机器人，跳过处理")
                return False
            
            # 安全检查
            if not SafetyChecker.is_safe_message(message):
                logger.warning(f"检测到不安全消息，拒绝处理: {message}")
                return False
            
            # 人性化延迟
            HumanLikeDelay.random_delay()
            
            # 获取AI回复
            logger.info("正在生成AI回复...")
            ai_reply = self.ai.get_qq_reply(message, user_id, group_id)
            
            if not ai_reply:
                logger.error("AI回复生成失败")
                ai_reply = ResponseFormatter.format_error_reply()
            
            # 格式化回复（@用户）
            reply_message = ResponseFormatter.format_at_reply(user_id, ai_reply)
            
            # 模拟打字延迟
            HumanLikeDelay.typing_delay(ai_reply)
            
            # 发送回复
            result = self.client.send_group_msg(group_id, reply_message)
            
            if result.get("status") == "ok":
                logger.info(f"群消息回复成功 - 群:{group_id}, 回复:{ai_reply[:50]}...")
                return True
            else:
                logger.error(f"群消息回复失败: {result}")
                return False
                
        except Exception as e:
            logger.error(f"处理群消息异常: {e}")
            return False
    
    def handle_private_message(self, message_data: Dict[str, Any]) -> bool:
        """
        处理私聊消息
        
        Args:
            message_data: 消息数据
            
        Returns:
            是否处理成功
        """
        try:
            user_id = str(message_data.get("user_id", ""))
            message = message_data.get("message", "")
            
            logger.info(f"收到私聊消息 - 用户:{user_id}, 内容:{message[:50]}...")
            
            # 安全检查
            if not SafetyChecker.is_safe_message(message):
                logger.warning(f"检测到不安全消息，拒绝处理: {message}")
                return False
            
            # 人性化延迟
            HumanLikeDelay.random_delay()
            
            # 获取AI回复
            ai_reply = self.ai.get_qq_reply(message, user_id)
            
            if not ai_reply:
                ai_reply = ResponseFormatter.format_error_reply()
            
            # 模拟打字延迟
            HumanLikeDelay.typing_delay(ai_reply)
            
            # 发送回复
            result = self.client.send_private_msg(user_id, ai_reply)
            
            if result.get("status") == "ok":
                logger.info(f"私聊回复成功 - 用户:{user_id}, 回复:{ai_reply[:50]}...")
                return True
            else:
                logger.error(f"私聊回复失败: {result}")
                return False
                
        except Exception as e:
            logger.error(f"处理私聊消息异常: {e}")
            return False
    
    def process_message(self, message_data: Dict[str, Any]) -> bool:
        """
        处理消息的统一入口
        
        Args:
            message_data: 消息数据
            
        Returns:
            是否处理成功
        """
        try:
            message_type = message_data.get("message_type", "")
            
            # 调用自定义回调
            for callback in self.message_callbacks:
                try:
                    callback(message_data)
                except Exception as e:
                    logger.error(f"消息回调执行异常: {e}")
            
            # 根据消息类型处理
            if message_type == "group":
                return self.handle_group_message(message_data)
            elif message_type == "private":
                return self.handle_private_message(message_data)
            else:
                logger.debug(f"未知消息类型: {message_type}")
                return False
                
        except Exception as e:
            logger.error(f"处理消息异常: {e}")
            return False

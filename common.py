"""
QQ机器人主类 - 整合所有功能模块
"""
from httpAPI import LLOneBotClient
from ai_interface import KimiA<PERSON>
from message_handler import MessageHandler
from message_query import MessageQuery
from qq_operations import QQOperations
from config import Config
from utils import logger
from typing import Dict, Any

class QQBot:
    """QQ机器人主类"""

    def __init__(self, client: LLOneBotClient = None, ai: KimiAI = None):
        """
        初始化QQ机器人

        Args:
            client: OneBot客户端，如果为None则使用默认配置创建
            ai: AI接口，如果为None则使用默认配置创建
        """
        # 验证配置
        if not Config.validate_config():
            logger.warning("配置验证失败，某些功能可能无法正常工作")

        # 初始化客户端
        self.client = client or LLOneBotClient(Config.ONEBOT_BASE_URL)

        # 初始化AI接口
        try:
            self.ai = ai or KimiAI()
            logger.info("AI接口初始化成功")
        except Exception as e:
            logger.error(f"AI接口初始化失败: {e}")
            self.ai = None

        # 初始化功能模块
        self.message_handler = MessageHandler(self.client, self.ai)
        self.message_query = MessageQuery(self.client)
        self.operations = QQOperations(self.client)

        # 机器人状态
        self.is_running = False
        self.bot_info = {}

        logger.info("QQ机器人初始化完成")

    def start(self) -> bool:
        """启动机器人"""
        try:
            logger.info("正在启动QQ机器人...")

            # 获取机器人信息
            self.bot_info = self.operations.get_bot_info()
            if not self.bot_info:
                logger.error("无法获取机器人信息，启动失败")
                return False

            # 测试AI连接（如果可用）
            if self.ai:
                if self.ai.test_connection():
                    logger.info("AI服务连接正常")
                else:
                    logger.warning("AI服务连接失败，将使用备用回复")

            self.is_running = True
            logger.info(f"QQ机器人启动成功 - {self.bot_info.get('nickname', 'Unknown')}")
            return True

        except Exception as e:
            logger.error(f"启动QQ机器人异常: {e}")
            return False

    def stop(self):
        """停止机器人"""
        self.is_running = False
        logger.info("QQ机器人已停止")

    def process_message(self, message_data: Dict[str, Any]) -> bool:
        """
        处理消息

        Args:
            message_data: 消息数据

        Returns:
            是否处理成功
        """
        if not self.is_running:
            logger.warning("机器人未启动，无法处理消息")
            return False

        return self.message_handler.process_message(message_data)

    def get_status(self) -> Dict[str, Any]:
        """获取机器人状态"""
        return {
            "is_running": self.is_running,
            "bot_info": self.bot_info,
            "ai_available": self.ai is not None,
            "config": Config.get_config()
        }
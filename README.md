# QQ机器人自动化框架

基于OneBot API和Kimi AI的QQ机器人，支持群聊和私聊智能回复。

## 功能特性

- 🤖 **群内@回复**: 被@时自动回复
- 💬 **私聊回复**: 私聊消息自动回复
- 🧠 **智能回复**: 特定群内智能回复（可配置概率）
- 🎭 **人性化**: 随机延迟、打字模拟
- ⚙️ **可配置**: 支持开关控制各种回复功能

## 项目结构

```
qqbot/
├── httpAPI.py          # OneBot API客户端
├── config.py           # 配置管理
├── utils.py            # 工具类（延迟、解析等）
├── ai_interface.py     # AI接口（Kimi）
├── message_handler.py  # 消息处理器
├── message_query.py    # 消息查询模块
├── qq_operations.py    # QQ常用操作
├── common.py           # 主机器人类
└── README.md           # 说明文档
```

## 快速开始

### 1. 环境配置

设置环境变量或修改 `config.py` 中的配置：

```bash
# OneBot API地址
export ONEBOT_BASE_URL="http://127.0.0.1:3000"

# 机器人QQ号
export BOT_QQ="你的机器人QQ号"

# Kimi AI配置（可选）
export KIMI_API_KEY="你的Kimi API Key"
export KIMI_BASE_URL="https://api.moonshot.cn/v1"
```

### 2. 安装依赖

```bash
pip install requests
```

### 3. 基本使用

```python
from common import QQBot

# 创建机器人实例
bot = QQBot()

# 启动机器人
if bot.start():
    print("机器人启动成功")
    
    # 处理消息（通常在消息接收回调中调用）
    message_data = {
        "message_type": "group",
        "group_id": "123456789",
        "user_id": "987654321",
        "message": "[CQ:at,qq=你的机器人QQ] 你好",
        "message_id": 12345
    }
    
    bot.process_message(message_data)
    
    # 停止机器人
    bot.stop()
```

## 主要功能

### 1. 消息处理

```python
# 自动处理@消息并回复
bot.message_handler.handle_group_message(message_data)

# 处理私聊消息
bot.message_handler.handle_private_message(message_data)
```

### 2. 消息查询

```python
# 查询群聊消息历史
messages = bot.message_query.get_group_messages("群号", limit=20)

# 分页查询
for message in bot.message_query.get_group_messages_paginated("群号", 100):
    print(message)

# 搜索消息
results = bot.message_query.search_group_messages("群号", "关键词")
```

### 3. 常用操作

```python
# 获取群列表
groups = bot.operations.get_group_list()

# 获取群成员
members = bot.operations.get_group_members("群号")

# 批量发送消息
targets = [
    {"type": "group", "id": "群号1"},
    {"type": "private", "id": "QQ号1"}
]
bot.operations.batch_send_message(targets, "消息内容")
```

### 4. AI回复

```python
# 获取AI回复
reply = bot.ai.get_qq_reply("用户消息", "用户QQ", "群号")
```

## 配置说明

### 基本配置

- `ONEBOT_BASE_URL`: OneBot API服务地址
- `BOT_QQ`: 机器人的QQ号
- `KIMI_API_KEY`: Kimi AI的API密钥

### 人性化配置

- `MIN_DELAY`: 最小延迟时间（秒）
- `MAX_DELAY`: 最大延迟时间（秒）
- `MESSAGE_QUERY_DELAY`: 消息查询间隔（秒）

### 功能配置

- `MAX_MESSAGE_LENGTH`: 最大消息长度
- `ENABLE_AT_REPLY`: 是否启用@回复

## 安全特性

1. **消息过滤**: 自动过滤危险操作关键词
2. **参数验证**: 验证QQ号格式等
3. **延迟控制**: 防止操作过于频繁
4. **错误处理**: 完善的异常处理机制

## 扩展开发

### 添加新的消息处理器

```python
def custom_message_callback(message_data):
    # 自定义消息处理逻辑
    pass

bot.message_handler.add_message_callback(custom_message_callback)
```

### 添加新的操作功能

继承或扩展 `QQOperations` 类：

```python
class CustomOperations(QQOperations):
    def custom_function(self):
        # 自定义功能
        pass
```

## 注意事项

1. **OneBot服务**: 需要先启动OneBot服务（如go-cqhttp）
2. **API限制**: 注意QQ API的调用频率限制
3. **账号安全**: 避免频繁操作，使用人性化延迟
4. **消息接收**: 本框架主要处理消息，需要配合消息接收服务使用

## 常见问题

### Q: 机器人无法启动？
A: 检查OneBot服务是否运行，配置是否正确

### Q: AI回复不工作？
A: 检查Kimi API Key是否配置正确，网络是否正常

### Q: 消息查询失败？
A: 确认机器人在目标群中，且有相应权限

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和平台规则。

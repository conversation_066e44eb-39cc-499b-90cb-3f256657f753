"""
消息监听器示例 - 演示如何接收和处理消息
"""
import json
import time
import threading
from typing import Dict, Any
from common import QQBot
from utils import logger

class MessageListener:
    """消息监听器"""
    
    def __init__(self, bot: QQBot):
        self.bot = bot
        self.running = False
        self.listen_thread = None
        
    def start_listening(self):
        """开始监听消息"""
        if self.running:
            logger.warning("消息监听器已在运行")
            return
        
        self.running = True
        self.listen_thread = threading.Thread(target=self._listen_loop, daemon=True)
        self.listen_thread.start()
        logger.info("消息监听器已启动")
    
    def stop_listening(self):
        """停止监听消息"""
        self.running = False
        if self.listen_thread:
            self.listen_thread.join(timeout=5)
        logger.info("消息监听器已停止")
    
    def _listen_loop(self):
        """监听循环（这里是模拟，实际需要接入WebSocket或HTTP回调）"""
        logger.info("开始监听消息...")
        
        while self.running:
            try:
                # 这里应该是实际的消息接收逻辑
                # 比如从WebSocket接收消息，或者处理HTTP回调
                # 现在只是演示如何处理消息
                
                time.sleep(1)  # 模拟等待消息
                
                # 在实际使用中，你会从OneBot服务接收到类似这样的消息
                # 这里只是演示消息格式
                
            except Exception as e:
                logger.error(f"消息监听异常: {e}")
                time.sleep(5)  # 出错后等待5秒再继续
    
    def handle_received_message(self, message_data: Dict[str, Any]):
        """
        处理接收到的消息
        
        Args:
            message_data: 从OneBot接收到的消息数据
        """
        try:
            # 记录接收到的消息
            msg_type = message_data.get("message_type", "unknown")
            logger.info(f"接收到{msg_type}消息: {json.dumps(message_data, ensure_ascii=False)[:200]}...")
            
            # 使用机器人处理消息
            success = self.bot.process_message(message_data)
            
            if success:
                logger.info("消息处理成功")
            else:
                logger.debug("消息未处理或处理失败")
                
        except Exception as e:
            logger.error(f"处理消息异常: {e}")

def simulate_messages(listener: MessageListener):
    """模拟一些消息来测试功能"""
    print("\n=== 模拟消息测试 ===")
    
    # 模拟群内@消息
    at_message = {
        "post_type": "message",
        "message_type": "group",
        "sub_type": "normal",
        "group_id": 607783611,
        "user_id": 123456789,
        "message": f"[CQ:at,qq=3420659474] 千千你好呀，最近LOL打得怎么样？",
        "message_id": 12345,
        "time": int(time.time()),
        "sender": {
            "user_id": 123456789,
            "nickname": "测试用户",
            "card": "",
            "role": "member"
        }
    }
    
    print("模拟群内@消息...")
    listener.handle_received_message(at_message)
    time.sleep(3)
    
    # 模拟私聊消息
    private_message = {
        "post_type": "message",
        "message_type": "private",
        "sub_type": "friend",
        "user_id": 987654321,
        "message": "千千，你的DNF大号是什么职业来着？",
        "message_id": 12346,
        "time": int(time.time()),
        "sender": {
            "user_id": 987654321,
            "nickname": "私聊测试用户",
            "age": 20,
            "sex": "unknown"
        }
    }
    
    print("模拟私聊消息...")
    listener.handle_received_message(private_message)
    time.sleep(3)
    
    # 模拟群内普通消息（智能回复）
    normal_message = {
        "post_type": "message",
        "message_type": "group",
        "sub_type": "normal",
        "group_id": 607783611,
        "user_id": 111222333,
        "message": "有人知道男刀怎么玩吗？求教！",
        "message_id": 12347,
        "time": int(time.time()),
        "sender": {
            "user_id": 111222333,
            "nickname": "萌新玩家",
            "card": "",
            "role": "member"
        }
    }
    
    print("模拟群内普通消息（可能触发智能回复）...")
    listener.handle_received_message(normal_message)
    time.sleep(3)
    
    # 模拟另一条普通消息
    normal_message2 = {
        "post_type": "message",
        "message_type": "group",
        "sub_type": "normal",
        "group_id": 607783611,
        "user_id": 444555666,
        "message": "今天天气真好",
        "message_id": 12348,
        "time": int(time.time()),
        "sender": {
            "user_id": 444555666,
            "nickname": "路人甲",
            "card": "",
            "role": "member"
        }
    }
    
    print("模拟群内普通消息2（可能不会回复）...")
    listener.handle_received_message(normal_message2)

def main():
    """主函数"""
    print("QQ机器人消息监听器演示")
    print("=" * 50)
    
    # 创建机器人
    bot = QQBot()
    
    # 启动机器人
    if not bot.start():
        print("机器人启动失败")
        return
    
    # 显示当前配置
    status = bot.get_status()
    reply_settings = status.get("reply_settings", {})
    print(f"\n当前回复设置:")
    print(f"  @回复: {'✓' if reply_settings.get('at_reply') else '✗'}")
    print(f"  私聊回复: {'✓' if reply_settings.get('private_reply') else '✗'}")
    print(f"  智能回复: {'✓' if reply_settings.get('smart_reply') else '✗'}")
    print(f"  智能回复群: {reply_settings.get('smart_reply_groups', [])}")
    print(f"  智能回复概率: {reply_settings.get('smart_reply_probability', 0)}")
    
    # 创建消息监听器
    listener = MessageListener(bot)
    
    # 模拟消息测试
    simulate_messages(listener)
    
    print("\n=== 测试完成 ===")
    print("在实际使用中，你需要：")
    print("1. 配置OneBot服务的HTTP回调或WebSocket")
    print("2. 将接收到的消息传递给 listener.handle_received_message()")
    print("3. 机器人会根据配置自动回复")
    
    # 停止机器人
    bot.stop()

if __name__ == "__main__":
    main()

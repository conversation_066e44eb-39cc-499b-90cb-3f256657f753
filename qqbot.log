2025-08-03 19:46:22,404 - utils - WARNING - 配置验证失败，某些功能可能无法正常工作
2025-08-03 19:46:22,404 - utils - ERROR - AI接口初始化失败: <PERSON><PERSON> API key is required
2025-08-03 21:47:09,510 - utils - INFO - Kimi AI initialized with model: moonshot-v1-8k
2025-08-03 21:47:09,510 - utils - INFO - AI接口初始化成功
2025-08-03 21:47:09,510 - utils - INFO - 消息处理器初始化完成
2025-08-03 21:47:09,510 - utils - INFO - 消息查询器初始化完成
2025-08-03 21:47:09,510 - utils - INFO - QQ操作模块初始化完成
2025-08-03 21:47:09,510 - utils - INFO - QQ机器人初始化完成
2025-08-03 21:47:09,511 - utils - INFO - 正在启动QQ机器人...
2025-08-03 21:47:12,267 - utils - ERROR - 获取机器人信息失败: {'status': 'error', 'error': "HTTPConnectionPool(host='127.0.0.1', port=3000): Max retries exceeded with url: /get_login_info (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000023F84694750>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}
2025-08-03 21:47:12,268 - utils - ERROR - 无法获取机器人信息，启动失败
2025-08-03 21:47:14,291 - utils - INFO - Kimi AI initialized with model: moonshot-v1-8k
2025-08-03 21:47:15,053 - utils - INFO - Kimi AI回复: 你好！有什么可以帮助你的吗？...
2025-08-03 21:47:15,978 - utils - INFO - Kimi AI回复: 嗨！你好呀~😄有什么可以帮到你的吗？...
2025-08-03 23:34:19,415 - utils - INFO - Kimi AI initialized with model: moonshot-v1-8k
2025-08-03 23:34:19,415 - utils - INFO - AI接口初始化成功
2025-08-03 23:34:19,415 - utils - INFO - 消息处理器初始化完成
2025-08-03 23:34:19,415 - utils - INFO - 消息查询器初始化完成
2025-08-03 23:34:19,415 - utils - INFO - QQ操作模块初始化完成
2025-08-03 23:34:19,415 - utils - INFO - QQ机器人初始化完成
2025-08-03 23:34:19,415 - utils - INFO - 正在启动QQ机器人...
2025-08-03 23:34:20,346 - utils - INFO - 机器人信息: 咪猫法魔 (**********)
2025-08-03 23:34:21,343 - utils - INFO - Kimi AI回复: 你好！有什么可以帮助你的吗？...
2025-08-03 23:34:21,344 - utils - INFO - AI服务连接正常
2025-08-03 23:34:21,344 - utils - INFO - QQ机器人启动成功 - 咪猫法魔
2025-08-03 23:34:21,344 - utils - INFO - QQ机器人已停止
2025-08-03 23:34:21,349 - utils - INFO - Kimi AI initialized with model: moonshot-v1-8k
2025-08-03 23:34:21,989 - utils - INFO - Kimi AI回复: 你好！有什么可以帮你的吗？...
2025-08-03 23:34:22,785 - utils - INFO - Kimi AI回复: 嗨，你好呀！👋有啥可以帮你的吗？...
2025-08-03 23:34:52,880 - utils - INFO - Kimi AI initialized with model: moonshot-v1-8k
2025-08-03 23:34:52,880 - utils - INFO - AI接口初始化成功
2025-08-03 23:34:52,880 - utils - INFO - 消息处理器初始化完成
2025-08-03 23:34:52,880 - utils - INFO - 消息查询器初始化完成
2025-08-03 23:34:52,880 - utils - INFO - QQ操作模块初始化完成
2025-08-03 23:34:52,896 - utils - INFO - QQ机器人初始化完成
2025-08-03 23:34:52,896 - utils - INFO - 正在启动QQ机器人...
2025-08-03 23:34:53,634 - utils - INFO - 机器人信息: 咪猫法魔 (**********)
2025-08-03 23:34:53,973 - utils - ERROR - Kimi API请求失败: 429 - {"error":{"message":"Your account org-0fcd4a494e1d4d68aee5122652f58eef\u003cak-f49o6m8rw6x111cyk8hi\u003e request reached organization max RPM: 3, please try again after 1 seconds","type":"rate_limit_reached_error"}}
2025-08-03 23:34:53,973 - utils - WARNING - AI服务连接失败，将使用备用回复
2025-08-03 23:34:53,973 - utils - INFO - QQ机器人启动成功 - 咪猫法魔
2025-08-03 23:35:19,614 - utils - INFO - 获取到 7 个好友
2025-08-03 23:35:55,592 - utils - INFO - Kimi AI回复: 嗨！我是群里的小助手，擅长聊天和分享知识。🤖 有啥不懂的尽管问我，我会尽力帮忙。聊天也能找乐子，一起...
2025-08-03 23:36:50,774 - utils - INFO - 获取到 2 个群
2025-08-03 23:38:34,659 - utils - INFO - QQ机器人已停止
2025-08-03 23:44:07,726 - utils - INFO - Kimi AI initialized with model: moonshot-v1-8k
2025-08-03 23:44:07,726 - utils - INFO - AI接口初始化成功
2025-08-03 23:44:07,726 - utils - INFO - 消息处理器初始化完成
2025-08-03 23:44:07,726 - utils - INFO - 消息查询器初始化完成
2025-08-03 23:44:07,726 - utils - INFO - QQ操作模块初始化完成
2025-08-03 23:44:07,726 - utils - INFO - QQ机器人初始化完成
2025-08-03 23:44:07,726 - utils - INFO - 正在启动QQ机器人...
2025-08-03 23:44:08,447 - utils - INFO - 机器人信息: 咪猫法魔 (**********)
2025-08-03 23:44:09,122 - utils - INFO - Kimi AI回复: 你好！有什么可以帮助你的吗？...
2025-08-03 23:44:09,122 - utils - INFO - AI服务连接正常
2025-08-03 23:44:09,123 - utils - INFO - QQ机器人启动成功 - 咪猫法魔
2025-08-03 23:44:22,509 - utils - INFO - QQ机器人已停止
2025-08-03 23:47:03,293 - utils - INFO - Kimi AI initialized with model: moonshot-v1-8k
2025-08-03 23:47:03,293 - utils - INFO - AI接口初始化成功
2025-08-03 23:47:03,293 - utils - INFO - 消息处理器初始化完成
2025-08-03 23:47:03,293 - utils - INFO - 消息查询器初始化完成
2025-08-03 23:47:03,293 - utils - INFO - QQ操作模块初始化完成
2025-08-03 23:47:03,293 - utils - INFO - QQ机器人初始化完成
2025-08-03 23:47:03,293 - utils - INFO - 正在启动QQ机器人...
2025-08-03 23:47:04,024 - utils - INFO - 机器人信息: 咪猫法魔 (**********)
2025-08-03 23:47:04,776 - utils - INFO - Kimi AI回复: 你好！有什么可以帮助你的吗？...
2025-08-03 23:47:04,778 - utils - INFO - AI服务连接正常
2025-08-03 23:47:04,778 - utils - INFO - QQ机器人启动成功 - 咪猫法魔
2025-08-03 23:47:04,780 - utils - INFO - 接收到group消息: {"post_type": "message", "message_type": "group", "sub_type": "normal", "group_id": *********, "user_id": 123456789, "message": "[CQ:at,qq=**********] 千千你好呀，最近LOL打得怎么样？", "message_id": 12345, "time": ...
2025-08-03 23:47:04,780 - utils - INFO - 收到群消息 - 群:*********, 用户:123456789, 内容:[CQ:at,qq=**********] 千千你好呀，最近LOL打得怎么样？...
2025-08-03 23:47:04,791 - utils - INFO - 触发@回复 - 群:*********
2025-08-03 23:47:06,747 - utils - INFO - 正在生成AI回复...
2025-08-03 23:47:08,199 - utils - INFO - Kimi AI回复: 千千酱这里～最近上分好难啊，卡在黄金上不去了QAQ。不过新英雄好强，准备试试能不能带我飞～召必回！...
2025-08-03 23:47:13,410 - utils - INFO - @回复成功 - 群:*********, 回复:千千酱这里～最近上分好难啊，卡在黄金上不去了QAQ。不过新英雄好强，准备试试能不能带我飞～召必回！...
2025-08-03 23:47:13,410 - utils - INFO - 消息处理成功
2025-08-03 23:47:16,412 - utils - INFO - 接收到private消息: {"post_type": "message", "message_type": "private", "sub_type": "friend", "user_id": 987654321, "message": "千千，你的DNF大号是什么职业来着？", "message_id": 12346, "time": 1754236036, "sender": {"user_id": 98765432...
2025-08-03 23:47:16,412 - utils - INFO - 收到私聊消息 - 用户:987654321, 内容:千千，你的DNF大号是什么职业来着？...
2025-08-03 23:47:18,412 - utils - INFO - 正在生成私聊AI回复...
2025-08-03 23:47:19,315 - utils - INFO - Kimi AI回复: 是元素师啦，灯光师哦。召必回！😳...
2025-08-03 23:47:31,110 - utils - ERROR - 私聊回复失败: {'status': 'failed', 'retcode': 200, 'data': None, 'message': 'invoke timeout, wrapperSession.getMsgService().sendMsg, 0,[object Object],[object Object],[object Map]', 'wording': 'invoke timeout, wrapperSession.getMsgService().sendMsg, 0,[object Object],[object Object],[object Map]'}
2025-08-03 23:47:34,112 - utils - INFO - 接收到group消息: {"post_type": "message", "message_type": "group", "sub_type": "normal", "group_id": *********, "user_id": 111222333, "message": "有人知道男刀怎么玩吗？求教！", "message_id": 12347, "time": 1754236054, "sender": {"u...
2025-08-03 23:47:34,112 - utils - INFO - 收到群消息 - 群:*********, 用户:111222333, 内容:有人知道男刀怎么玩吗？求教！...
2025-08-03 23:47:37,114 - utils - INFO - 接收到group消息: {"post_type": "message", "message_type": "group", "sub_type": "normal", "group_id": *********, "user_id": *********, "message": "今天天气真好", "message_id": 12348, "time": **********, "sender": {"user_id":...
2025-08-03 23:47:37,114 - utils - INFO - 收到群消息 - 群:*********, 用户:*********, 内容:今天天气真好...
2025-08-03 23:47:37,115 - utils - INFO - 触发智能回复 - 群:*********
2025-08-03 23:47:38,147 - utils - INFO - 正在生成AI回复...
2025-08-03 23:47:38,401 - utils - ERROR - Kimi API请求失败: 429 - {"error":{"message":"Your account org-0fcd4a494e1d4d68aee5122652f58eef\u003cak-f49o6m8rw6x111cyk8hi\u003e request reached organization max RPM: 3, please try again after 1 seconds","type":"rate_limit_reached_error"}}
2025-08-03 23:47:39,955 - utils - INFO - 智能回复成功 - 群:*********, 回复:网络有点卡，请稍后再试...
2025-08-03 23:47:39,955 - utils - INFO - 消息处理成功
2025-08-03 23:47:39,956 - utils - INFO - QQ机器人已停止
2025-08-03 23:55:28,931 - utils - INFO - Kimi AI initialized with model: moonshot-v1-8k
2025-08-03 23:55:28,931 - utils - INFO - AI接口初始化成功
2025-08-03 23:55:28,931 - utils - INFO - 消息处理器初始化完成
2025-08-03 23:55:28,931 - utils - INFO - 消息查询器初始化完成
2025-08-03 23:55:28,931 - utils - INFO - QQ操作模块初始化完成
2025-08-03 23:55:28,931 - utils - INFO - QQ机器人初始化完成
2025-08-03 23:55:28,931 - utils - INFO - 正在启动QQ机器人...
2025-08-03 23:55:29,647 - utils - INFO - 机器人信息: 咪猫法魔 (**********)
2025-08-03 23:55:32,276 - utils - INFO - Kimi AI回复: 你好！有什么可以帮助你的吗？...
2025-08-03 23:55:32,277 - utils - INFO - AI服务连接正常
2025-08-03 23:55:32,277 - utils - INFO - QQ机器人启动成功 - 咪猫法魔
2025-08-04 00:05:00,332 - utils - INFO - Kimi AI initialized with model: moonshot-v1-8k
2025-08-04 00:05:00,332 - utils - INFO - AI接口初始化成功
2025-08-04 00:05:00,332 - utils - INFO - 消息处理器初始化完成
2025-08-04 00:05:00,332 - utils - INFO - 消息查询器初始化完成
2025-08-04 00:05:00,332 - utils - INFO - QQ操作模块初始化完成
2025-08-04 00:05:00,332 - utils - INFO - QQ机器人初始化完成
2025-08-04 00:05:00,332 - utils - INFO - 正在启动QQ机器人...
2025-08-04 00:05:01,157 - utils - INFO - 机器人信息: 咪猫法魔 (**********)
2025-08-04 00:05:01,907 - utils - INFO - Kimi AI回复: 你好！有什么可以帮你的吗？...
2025-08-04 00:05:01,908 - utils - INFO - AI服务连接正常
2025-08-04 00:05:01,908 - utils - INFO - QQ机器人启动成功 - 咪猫法魔
2025-08-04 00:05:01,912 - websocket - INFO - Websocket connected
2025-08-04 00:05:24,637 - utils - INFO - 收到私聊消息 - 用户:1120707776, 内容:[{'type': 'text', 'data': {'text': 'o.0?'}}]...
2025-08-04 00:05:24,639 - utils - INFO - 收到私聊消息 - 用户:1120707776, 内容:[{'type': 'text', 'data': {'text': 'o.0?'}}]...
2025-08-04 00:05:24,639 - utils - ERROR - 处理私聊消息异常: 'list' object has no attribute 'lower'
2025-08-04 00:05:39,482 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '第二那个'}}]...
2025-08-04 00:05:39,482 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '第二那个'}}]...
2025-08-04 00:05:39,482 - utils - ERROR - 处理群消息异常: 'list' object has no attribute 'lower'
2025-08-04 00:05:42,449 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '还有个B神器'}}]...
2025-08-04 00:05:42,449 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '还有个B神器'}}]...
2025-08-04 00:05:42,450 - utils - ERROR - 处理群消息异常: 'list' object has no attribute 'lower'
2025-08-04 00:05:43,460 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '服了'}}]...
2025-08-04 00:05:43,460 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '服了'}}]...
2025-08-04 00:05:43,461 - utils - ERROR - 处理群消息异常: 'list' object has no attribute 'lower'
2025-08-04 00:05:44,451 - utils - INFO - 收到私聊消息 - 用户:1120707776, 内容:[{'type': 'text', 'data': {'text': '你是谁'}}]...
2025-08-04 00:05:44,452 - utils - INFO - 收到私聊消息 - 用户:1120707776, 内容:[{'type': 'text', 'data': {'text': '你是谁'}}]...
2025-08-04 00:05:44,452 - utils - ERROR - 处理私聊消息异常: 'list' object has no attribute 'lower'
2025-08-04 00:06:48,574 - utils - INFO - 收到群消息 - 群:*********, 用户:354427196, 内容:[{'type': 'text', 'data': {'text': '4个咯'}}]...
2025-08-04 00:06:48,574 - utils - INFO - 收到群消息 - 群:*********, 用户:354427196, 内容:[{'type': 'text', 'data': {'text': '4个咯'}}]...
2025-08-04 00:06:48,574 - utils - ERROR - 处理群消息异常: 'list' object has no attribute 'lower'
2025-08-04 00:07:14,390 - utils - INFO - QQ机器人已停止
2025-08-04 00:07:14,390 - utils - INFO - QQ机器人已停止
2025-08-04 00:07:41,688 - utils - INFO - Kimi AI initialized with model: moonshot-v1-8k
2025-08-04 00:07:41,688 - utils - INFO - AI接口初始化成功
2025-08-04 00:07:41,688 - utils - INFO - 消息处理器初始化完成
2025-08-04 00:07:41,688 - utils - INFO - 消息查询器初始化完成
2025-08-04 00:07:41,688 - utils - INFO - QQ操作模块初始化完成
2025-08-04 00:07:41,688 - utils - INFO - QQ机器人初始化完成
2025-08-04 00:07:41,688 - utils - INFO - 正在启动QQ机器人...
2025-08-04 00:07:42,466 - utils - INFO - 机器人信息: 咪猫法魔 (**********)
2025-08-04 00:07:43,071 - utils - INFO - Kimi AI回复: 你好！有什么可以帮你的吗？...
2025-08-04 00:07:43,072 - utils - INFO - AI服务连接正常
2025-08-04 00:07:43,072 - utils - INFO - QQ机器人启动成功 - 咪猫法魔
2025-08-04 00:07:43,077 - websocket - INFO - Websocket connected
2025-08-04 00:07:45,049 - utils - INFO - 收到群消息 - 群:*********, 用户:308329163, 内容:[{'type': 'text', 'data': {'text': '不是全删除吗'}}]...
2025-08-04 00:07:45,050 - utils - INFO - 收到群消息 - 群:*********, 用户:308329163, 内容:[{'type': 'text', 'data': {'text': '不是全删除吗'}}]...
2025-08-04 00:07:45,050 - utils - WARNING - 检测到潜在危险操作: 删除
2025-08-04 00:07:45,050 - utils - WARNING - 检测到不安全消息，拒绝处理: [{'type': 'text', 'data': {'text': '不是全删除吗'}}]
2025-08-04 00:07:56,557 - utils - INFO - QQ机器人已停止
2025-08-04 00:07:56,557 - utils - INFO - QQ机器人已停止
2025-08-04 00:08:15,585 - utils - INFO - Kimi AI initialized with model: moonshot-v1-8k
2025-08-04 00:08:15,585 - utils - INFO - AI接口初始化成功
2025-08-04 00:08:15,585 - utils - INFO - 消息处理器初始化完成
2025-08-04 00:08:15,585 - utils - INFO - 消息查询器初始化完成
2025-08-04 00:08:15,585 - utils - INFO - QQ操作模块初始化完成
2025-08-04 00:08:15,585 - utils - INFO - QQ机器人初始化完成
2025-08-04 00:08:15,585 - utils - INFO - 正在启动QQ机器人...
2025-08-04 00:08:16,426 - utils - INFO - 机器人信息: 咪猫法魔 (**********)
2025-08-04 00:08:16,991 - utils - INFO - Kimi AI回复: 你好！有什么可以帮助你的吗？...
2025-08-04 00:08:16,992 - utils - INFO - AI服务连接正常
2025-08-04 00:08:16,992 - utils - INFO - QQ机器人启动成功 - 咪猫法魔
2025-08-04 00:08:16,996 - websocket - INFO - Websocket connected
2025-08-04 00:08:26,661 - utils - INFO - 收到私聊消息 - 用户:1120707776, 内容:[{'type': 'text', 'data': {'text': 'o.0？'}}]...
2025-08-04 00:08:26,661 - utils - INFO - 收到私聊消息 - 用户:1120707776, 内容:[{'type': 'text', 'data': {'text': 'o.0？'}}]...
2025-08-04 00:08:28,896 - utils - INFO - 正在生成私聊AI回复...
2025-08-04 00:08:29,772 - utils - INFO - Kimi AI回复: 啊，这是啥意思呀... 😳 狗迷人？...
2025-08-04 00:08:31,803 - utils - INFO - 私聊回复成功 - 用户:1120707776, 回复:啊，这是啥意思呀... 😳 狗迷人？...
2025-08-04 00:08:31,803 - utils - INFO - 消息处理成功
2025-08-04 00:08:31,804 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '那个没啥用'}}]...
2025-08-04 00:08:31,804 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '那个没啥用'}}]...
2025-08-04 00:08:47,128 - utils - INFO - QQ机器人已停止
2025-08-04 00:08:47,128 - utils - INFO - QQ机器人已停止
2025-08-04 00:09:12,945 - utils - INFO - Kimi AI initialized with model: moonshot-v1-8k
2025-08-04 00:09:12,945 - utils - INFO - AI接口初始化成功
2025-08-04 00:09:12,945 - utils - INFO - 消息处理器初始化完成
2025-08-04 00:09:12,945 - utils - INFO - 消息查询器初始化完成
2025-08-04 00:09:12,945 - utils - INFO - QQ操作模块初始化完成
2025-08-04 00:09:12,945 - utils - INFO - QQ机器人初始化完成
2025-08-04 00:09:12,945 - utils - INFO - 正在启动QQ机器人...
2025-08-04 00:09:13,747 - utils - INFO - 机器人信息: 咪猫法魔 (**********)
2025-08-04 00:09:14,339 - utils - INFO - Kimi AI回复: 你好！有什么可以帮助你的吗？...
2025-08-04 00:09:14,344 - utils - INFO - AI服务连接正常
2025-08-04 00:09:14,344 - utils - INFO - QQ机器人启动成功 - 咪猫法魔
2025-08-04 00:09:14,345 - websocket - INFO - Websocket connected
2025-08-04 00:09:55,719 - utils - INFO - QQ机器人已停止
2025-08-04 00:09:55,720 - utils - INFO - QQ机器人已停止
2025-08-04 00:10:09,608 - utils - INFO - Kimi AI initialized with model: moonshot-v1-8k
2025-08-04 00:10:09,608 - utils - INFO - AI接口初始化成功
2025-08-04 00:10:09,608 - utils - INFO - 消息处理器初始化完成
2025-08-04 00:10:09,608 - utils - INFO - 消息查询器初始化完成
2025-08-04 00:10:09,608 - utils - INFO - QQ操作模块初始化完成
2025-08-04 00:10:09,608 - utils - INFO - QQ机器人初始化完成
2025-08-04 00:10:09,608 - utils - INFO - 正在启动QQ机器人...
2025-08-04 00:10:10,309 - utils - INFO - 机器人信息: 咪猫法魔 (**********)
2025-08-04 00:10:10,939 - utils - INFO - Kimi AI回复: 你好！有什么可以帮助你的吗？...
2025-08-04 00:10:10,939 - utils - INFO - AI服务连接正常
2025-08-04 00:10:10,939 - utils - INFO - QQ机器人启动成功 - 咪猫法魔
2025-08-04 00:10:10,941 - websocket - INFO - Websocket connected
2025-08-04 00:10:24,487 - utils - INFO - 收到群消息 - 群:*********, 用户:1120707776, 内容:[{'type': 'text', 'data': {'text': '真没机制吗'}}]...
2025-08-04 00:10:24,488 - utils - INFO - 收到群消息 - 群:*********, 用户:1120707776, 内容:[{'type': 'text', 'data': {'text': '真没机制吗'}}]...
2025-08-04 00:10:24,488 - utils - INFO - 触发智能回复 - 群:*********
2025-08-04 00:10:26,549 - utils - INFO - 正在生成AI回复...
2025-08-04 00:10:28,152 - utils - INFO - Kimi AI回复: emmm，机制这种东西嘛，其实挺复杂的，看个人理解啦。有时候，简单的操作也能打出意想不到的效果，对吧...
2025-08-04 00:10:33,486 - utils - INFO - 智能回复成功 - 群:*********, 回复:emmm，机制这种东西嘛，其实挺复杂的，看个人理解啦。有时候，简单的操作也能打出意想不到的效果，对吧...
2025-08-04 00:10:33,486 - utils - INFO - 消息处理成功
2025-08-04 00:10:42,679 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '感觉小弟'}}]...
2025-08-04 00:10:42,679 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '感觉小弟'}}]...
2025-08-04 00:10:45,013 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '速8了'}}]...
2025-08-04 00:10:45,014 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '速8了'}}]...
2025-08-04 00:10:49,398 - utils - INFO - 收到群消息 - 群:*********, 用户:354427196, 内容:[{'type': 'text', 'data': {'text': '无所谓'}}]...
2025-08-04 00:10:49,398 - utils - INFO - 收到群消息 - 群:*********, 用户:354427196, 内容:[{'type': 'text', 'data': {'text': '无所谓'}}]...
2025-08-04 00:11:32,649 - utils - INFO - 收到群消息 - 群:*********, 用户:308329163, 内容:[{'type': 'text', 'data': {'text': '卧槽 基地对冲忘记吃了'}}...
2025-08-04 00:11:32,649 - utils - INFO - 收到群消息 - 群:*********, 用户:308329163, 内容:[{'type': 'text', 'data': {'text': '卧槽 基地对冲忘记吃了'}}]...
2025-08-04 00:11:32,649 - utils - INFO - 触发智能回复 - 群:*********
2025-08-04 00:11:34,114 - utils - INFO - 正在生成AI回复...
2025-08-04 00:11:35,131 - utils - INFO - Kimi AI回复: 啊，这...不要慌，下把记得吃就好。😅召必回！...
2025-08-04 00:11:38,677 - utils - INFO - 智能回复成功 - 群:*********, 回复:啊，这...不要慌，下把记得吃就好。😅召必回！...
2025-08-04 00:11:38,677 - utils - INFO - 消息处理成功
2025-08-04 00:11:38,678 - utils - INFO - 收到群消息 - 群:*********, 用户:308329163, 内容:[{'type': 'text', 'data': {'text': '一直吃50'}}]...
2025-08-04 00:11:38,678 - utils - INFO - 收到群消息 - 群:*********, 用户:308329163, 内容:[{'type': 'text', 'data': {'text': '一直吃50'}}]...
2025-08-04 00:11:38,678 - utils - INFO - 触发智能回复 - 群:*********
2025-08-04 00:11:40,722 - utils - INFO - 正在生成AI回复...
2025-08-04 00:11:41,695 - utils - INFO - Kimi AI回复: 真的吗，那可得好好奖励自己一把男刀了。😌...
2025-08-04 00:11:44,069 - utils - INFO - 智能回复成功 - 群:*********, 回复:真的吗，那可得好好奖励自己一把男刀了。😌...
2025-08-04 00:11:44,069 - utils - INFO - 消息处理成功
2025-08-04 00:11:44,952 - utils - INFO - 收到群消息 - 群:*********, 用户:381642657, 内容:[{'type': 'reply', 'data': {'id': '2022533305'}}, ...
2025-08-04 00:11:44,953 - utils - INFO - 收到群消息 - 群:*********, 用户:381642657, 内容:[{'type': 'reply', 'data': {'id': '2022533305'}}, {'type': 'at', 'data': {'qq': '**********', 'name': '千千'}}, {'type': 'text', 'data': {'text': ' 召必回是什么东西'}}]...
2025-08-04 00:11:44,953 - utils - INFO - 触发@回复 - 群:*********
2025-08-04 00:11:47,431 - utils - INFO - 正在生成AI回复...
2025-08-04 00:11:48,928 - utils - INFO - Kimi AI回复: 嘿嘿，召必回就是我的一种口头禅啦～意思是无论何时被召唤，我都会立刻回应，就像DNF里召必回的召唤兽一...
2025-08-04 00:11:54,264 - utils - INFO - @回复成功 - 群:*********, 回复:嘿嘿，召必回就是我的一种口头禅啦～意思是无论何时被召唤，我都会立刻回应，就像DNF里召必回的召唤兽一...
2025-08-04 00:11:54,264 - utils - INFO - 消息处理成功
2025-08-04 00:12:15,777 - utils - INFO - 收到群消息 - 群:*********, 用户:381642657, 内容:[{'type': 'text', 'data': {'text': '逻辑还不太行'}}]...
2025-08-04 00:12:15,777 - utils - INFO - 收到群消息 - 群:*********, 用户:381642657, 内容:[{'type': 'text', 'data': {'text': '逻辑还不太行'}}]...
2025-08-04 00:12:15,777 - utils - INFO - 触发智能回复 - 群:*********
2025-08-04 00:12:17,706 - utils - INFO - 正在生成AI回复...
2025-08-04 00:12:18,770 - utils - INFO - Kimi AI回复: 啊，这个嘛... 逻辑游戏确实挺难的，需要多练习呢。召必回！😅...
2025-08-04 00:12:22,679 - utils - INFO - 智能回复成功 - 群:*********, 回复:啊，这个嘛... 逻辑游戏确实挺难的，需要多练习呢。召必回！😅...
2025-08-04 00:12:22,680 - utils - INFO - 消息处理成功
2025-08-04 00:12:37,306 - utils - INFO - 收到群消息 - 群:*********, 用户:381642657, 内容:[{'type': 'text', 'data': {'text': '粗劣的模仿'}}]...
2025-08-04 00:12:37,307 - utils - INFO - 收到群消息 - 群:*********, 用户:381642657, 内容:[{'type': 'text', 'data': {'text': '粗劣的模仿'}}]...
2025-08-04 00:12:59,115 - utils - INFO - 收到群消息 - 群:*********, 用户:381642657, 内容:[{'type': 'text', 'data': {'text': 'ai还无法代替千神'}}]...
2025-08-04 00:12:59,115 - utils - INFO - 收到群消息 - 群:*********, 用户:381642657, 内容:[{'type': 'text', 'data': {'text': 'ai还无法代替千神'}}]...
2025-08-04 00:13:06,150 - utils - INFO - 收到群消息 - 群:*********, 用户:1120707776, 内容:[{'type': 'text', 'data': {'text': '尚未训练，直接接的生肉kim...
2025-08-04 00:13:06,150 - utils - INFO - 收到群消息 - 群:*********, 用户:1120707776, 内容:[{'type': 'text', 'data': {'text': '尚未训练，直接接的生肉kimi'}}]...
2025-08-04 00:13:17,466 - utils - INFO - 收到群消息 - 群:*********, 用户:1120707776, 内容:[{'type': 'text', 'data': {'text': '没喂肥千的语料'}}]...
2025-08-04 00:13:17,466 - utils - INFO - 收到群消息 - 群:*********, 用户:1120707776, 内容:[{'type': 'text', 'data': {'text': '没喂肥千的语料'}}]...
2025-08-04 00:14:51,554 - utils - INFO - QQ机器人已停止
2025-08-04 00:14:51,554 - utils - INFO - QQ机器人已停止
2025-08-04 00:15:53,606 - utils - INFO - Kimi AI initialized with model: moonshot-v1-8k
2025-08-04 00:15:53,606 - utils - INFO - AI接口初始化成功
2025-08-04 00:15:53,606 - utils - INFO - 消息处理器初始化完成
2025-08-04 00:15:53,606 - utils - INFO - 消息查询器初始化完成
2025-08-04 00:15:53,606 - utils - INFO - QQ操作模块初始化完成
2025-08-04 00:15:53,606 - utils - INFO - QQ机器人初始化完成
2025-08-04 00:15:53,606 - utils - INFO - 正在启动QQ机器人...
2025-08-04 00:15:54,532 - utils - INFO - 机器人信息: 咪猫法魔 (**********)
2025-08-04 00:15:55,129 - utils - INFO - Kimi AI回复: 你好！有什么可以帮助你的吗？...
2025-08-04 00:15:55,130 - utils - INFO - AI服务连接正常
2025-08-04 00:15:55,130 - utils - INFO - QQ机器人启动成功 - 咪猫法魔
2025-08-04 00:15:55,134 - websocket - INFO - Websocket connected
2025-08-04 00:16:02,379 - utils - INFO - 收到群消息 - 群:*********, 用户:1120707776, 内容:[{'type': 'face', 'data': {'id': '300', 'sub_type': 2}}]...
2025-08-04 00:17:05,003 - utils - INFO - 收到群消息 - 群:*********, 用户:1120707776, 内容:[{'type': 'text', 'data': {'text': '估计只能弄个参数小点的模型了，不能接大公司的api，他们的api没法训练'}}]...
2025-08-04 00:17:55,171 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '卡特大人无敌了吗'}}]...
2025-08-04 00:17:55,171 - utils - INFO - 触发智能回复 - 群:*********
2025-08-04 00:17:57,463 - utils - INFO - 正在生成AI回复...
2025-08-04 00:17:58,732 - utils - INFO - Kimi AI回复: 卡特大人确实挺强的，但说无敌还不至于。。。得看操作啦，嘿嘿。召必回。...
2025-08-04 00:18:02,659 - utils - INFO - 智能回复成功 - 群:*********, 回复:卡特大人确实挺强的，但说无敌还不至于。。。得看操作啦，嘿嘿。召必回。...
2025-08-04 00:18:02,659 - utils - INFO - 消息处理成功
2025-08-04 00:18:04,672 - utils - INFO - QQ机器人已停止
2025-08-04 00:18:04,672 - utils - INFO - QQ机器人已停止
2025-08-04 00:18:14,175 - utils - INFO - Kimi AI initialized with model: moonshot-v1-8k
2025-08-04 00:18:14,175 - utils - INFO - AI接口初始化成功
2025-08-04 00:18:14,175 - utils - INFO - 消息处理器初始化完成
2025-08-04 00:18:14,175 - utils - INFO - 消息查询器初始化完成
2025-08-04 00:18:14,175 - utils - INFO - QQ操作模块初始化完成
2025-08-04 00:18:14,175 - utils - INFO - QQ机器人初始化完成
2025-08-04 00:18:14,175 - utils - INFO - 正在启动QQ机器人...
2025-08-04 00:18:14,977 - utils - INFO - 机器人信息: 咪猫法魔 (**********)
2025-08-04 00:18:15,695 - utils - INFO - Kimi AI回复: 你好！有什么可以帮你的吗？...
2025-08-04 00:18:15,696 - utils - INFO - AI服务连接正常
2025-08-04 00:18:15,696 - utils - INFO - QQ机器人启动成功 - 咪猫法魔
2025-08-04 00:18:15,697 - websocket - INFO - Websocket connected
2025-08-04 00:18:17,101 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '这么有实力'}}]...
2025-08-04 00:18:35,701 - utils - INFO - 收到群消息 - 群:*********, 用户:1120707776, 内容:[{'type': 'text', 'data': {'text': '强无敌'}}]...
2025-08-04 00:18:55,054 - utils - INFO - 收到群消息 - 群:*********, 用户:1120707776, 内容:[{'type': 'text', 'data': {'text': '洗澡去，挂在这一会'}}]...
2025-08-04 00:20:57,567 - utils - INFO - 收到群消息 - 群:*********, 用户:354427196, 内容:[{'type': 'text', 'data': {'text': '又玩恶心东西？'}}]...
2025-08-04 00:20:57,568 - utils - INFO - 触发智能回复 - 群:*********
2025-08-04 00:20:59,727 - utils - INFO - 正在生成AI回复...
2025-08-04 00:21:00,933 - utils - INFO - Kimi AI回复: 嗯哼？🤔 又是谁在背后说我坏话呢。那真得奖励自己一把男刀了。😏...
2025-08-04 00:21:03,754 - utils - INFO - 智能回复成功 - 群:*********, 回复:嗯哼？🤔 又是谁在背后说我坏话呢。那真得奖励自己一把男刀了。😏...
2025-08-04 00:21:03,754 - utils - INFO - 消息处理成功
2025-08-04 00:21:07,678 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '怎么恶心了'}}]...
2025-08-04 00:21:50,774 - utils - INFO - 收到群消息 - 群:*********, 用户:792514112, 内容:[{'type': 'text', 'data': {'text': '只会男刀？'}}, {'type': 'at', 'data': {'qq': '**********', 'name': '千千'}}, {'type': 'text', 'data': {'text': ' '}}]...
2025-08-04 00:21:50,774 - utils - INFO - 触发@回复 - 群:*********
2025-08-04 00:21:53,309 - utils - INFO - 正在生成AI回复...
2025-08-04 00:21:54,252 - utils - INFO - Kimi AI回复: 那可不，男刀玩得溜。😏 别小看人。...
2025-08-04 00:21:56,013 - utils - INFO - @回复成功 - 群:*********, 回复:那可不，男刀玩得溜。😏 别小看人。...
2025-08-04 00:21:56,013 - utils - INFO - 消息处理成功
2025-08-04 00:22:45,944 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'image', 'data': {'file': '2B335FDE38EB5E467F52188694D56B80.png', 'subType': 0, 'url': 'https://multimedia.nt.qq.com.cn/download?appid=1407&fileid=EhRQ2sWk6gM4LdGA7Ev5tY9mUNZX-Bj8hcsBIP8KKJnusrSG744DMgRwcm9kUIC9owFaEJ9CS2IPITsvk69b_UxBVgZ6AtEx&spec=0&rkey=CAMSMO9g-wXH9EpvyXBvBcHlZq_ytrA8juuOBfSRJ6AyHiGqNX47bQyGjj60VazRFXpiNQ', 'file_size': '3326716'}}, {'type': 'text', 'data': {'text': '这样有输出吗'}}]...
2025-08-04 00:22:45,945 - utils - INFO - 触发智能回复 - 群:*********
2025-08-04 00:22:47,716 - utils - INFO - 正在生成AI回复...
2025-08-04 00:22:48,578 - utils - INFO - Kimi AI回复: 嗯，输出要看位置和操作的。真的吗？...
2025-08-04 00:22:50,763 - utils - INFO - 智能回复成功 - 群:*********, 回复:嗯，输出要看位置和操作的。真的吗？...
2025-08-04 00:22:50,763 - utils - INFO - 消息处理成功
2025-08-04 00:25:29,494 - utils - INFO - 收到群消息 - 群:*********, 用户:1604074248, 内容:[{'type': 'text', 'data': {'text': '说话不够绿茶'}}]...
2025-08-04 00:27:02,858 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '大滴又走了'}}]...
2025-08-04 00:27:02,858 - utils - INFO - 触发智能回复 - 群:*********
2025-08-04 00:27:04,849 - utils - INFO - 正在生成AI回复...
2025-08-04 00:27:05,776 - utils - INFO - Kimi AI回复: 真的吗？那真得奖励自己一把男刀了。...
2025-08-04 00:27:08,002 - utils - INFO - 智能回复成功 - 群:*********, 回复:真的吗？那真得奖励自己一把男刀了。...
2025-08-04 00:27:08,002 - utils - INFO - 消息处理成功
2025-08-04 00:30:26,685 - utils - INFO - 收到群消息 - 群:1014346539, 用户:1123008033, 内容:[{'type': 'image', 'data': {'file': 'A90E8B83D379EAADCF132F900FE7B952.png', 'subType': 0, 'url': 'https://multimedia.nt.qq.com.cn/download?appid=1407&fileid=EhRE-hcuAvLYUUgBU6VmNG9N6PbXZxiW0wwg_woon6eTkYjvjgMyBHByb2RQgL2jAVoQz7x18FXX3DTwuMs7Sr0lKXoCZeE&spec=0&rkey=CAESMNNVPZfw5rX3H5XeBITYb0fU7zm0e5uiUzDyVMJySSXyIZ5GeoMeq4g_uhVDvokh3A', 'file_size': '207254'}}]...
2025-08-04 00:30:34,651 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '我日 不会没分吧'}}]...
2025-08-04 00:30:41,437 - utils - INFO - 收到群消息 - 群:1014346539, 用户:1123008033, 内容:[{'type': 'face', 'data': {'id': '13', 'sub_type': 1}}, {'type': 'face', 'data': {'id': '13', 'sub_type': 1}}, {'type': 'face', 'data': {'id': '13', 'sub_type': 1}}]...
2025-08-04 00:31:44,455 - utils - INFO - 收到群消息 - 群:*********, 用户:381642657, 内容:[{'type': 'text', 'data': {'text': '不如先生成我吧'}}]...
2025-08-04 00:31:47,367 - utils - INFO - 收到群消息 - 群:*********, 用户:381642657, 内容:[{'type': 'image', 'data': {'file': 'A8B66B907732B72800F834AB9BAB6D47.jpg', 'subType': 1, 'url': 'https://multimedia.nt.qq.com.cn/download?appid=1407&fileid=EhRLcUGX3l922W9gDMuTYJe__5c4PxihikUg_woo4MLpt4jvjgMyBHByb2RQgL2jAVoQ168ObLfjcexGTJIHbV8s-HoC2YM&spec=0&rkey=CAESMNNVPZfw5rX3H5XeBITYb0fU7zm0e5uiUzDyVMJySSXyIZ5GeoMeq4g_uhVDvokh3A', 'file_size': '1131809'}}]...
2025-08-04 00:34:27,190 - utils - INFO - 收到群消息 - 群:*********, 用户:354427196, 内容:[{'type': 'text', 'data': {'text': '废物'}}]...
2025-08-04 00:34:29,124 - utils - INFO - 收到群消息 - 群:*********, 用户:308329163, 内容:[{'type': 'text', 'data': {'text': '出生'}}]...
2025-08-04 00:34:30,038 - utils - INFO - 收到群消息 - 群:*********, 用户:354427196, 内容:[{'type': 'image', 'data': {'file': '{A8B66B90-7732-B728-00F8-34AB9BAB6D47}.gif', 'subType': 0, 'url': 'https://gchat.qpic.cn/gchatpic_new/354427196/*********-2176934570-A8B66B907732B72800F834AB9BAB6D47/0?term=255&is_origin=0', 'file_size': '1131809'}}]...
2025-08-04 00:34:38,702 - utils - INFO - 收到群消息 - 群:*********, 用户:354427196, 内容:[{'type': 'text', 'data': {'text': '断我水晶那个分都没有'}}]...
2025-08-04 00:34:40,623 - utils - INFO - 收到群消息 - 群:*********, 用户:354427196, 内容:[{'type': 'text', 'data': {'text': '傻逼一个'}}]...
2025-08-04 00:34:44,492 - utils - INFO - 收到群消息 - 群:*********, 用户:354427196, 内容:[{'type': 'text', 'data': {'text': '80多血突然摆烂'}}]...
2025-08-04 00:34:47,314 - utils - INFO - 收到群消息 - 群:*********, 用户:354427196, 内容:[{'type': 'text', 'data': {'text': '有毛病的'}}]...
2025-08-04 00:34:52,615 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'image', 'data': {'file': 'A8B66B907732B72800F834AB9BAB6D47.gif', 'subType': 0, 'url': 'https://multimedia.nt.qq.com.cn/download?appid=1407&fileid=EhRLcUGX3l922W9gDMuTYJe__5c4PxihikUg_wooquKdkInvjgMyBHByb2RQgL2jAVoQ3cZY8jGmddJG5-Hz5IPxtXoCHOc&spec=0&rkey=CAESMNNVPZfw5rX3H5XeBITYb0fU7zm0e5uiUzDyVMJySSXyIZ5GeoMeq4g_uhVDvokh3A', 'file_size': '1131809'}}, {'type': 'text', 'data': {'text': '狗人'}}]...
2025-08-04 00:34:54,422 - utils - INFO - 收到群消息 - 群:*********, 用户:354427196, 内容:[{'type': 'text', 'data': {'text': '洗澡睡觉了'}}]...
2025-08-04 00:34:57,739 - utils - INFO - 收到群消息 - 群:*********, 用户:354427196, 内容:[{'type': 'image', 'data': {'file': '{A8B66B90-7732-B728-00F8-34AB9BAB6D47}.gif', 'subType': 0, 'url': 'https://gchat.qpic.cn/gchatpic_new/354427196/*********-2196962864-A8B66B907732B72800F834AB9BAB6D47/0?term=255&is_origin=0', 'file_size': '1131809'}}]...
2025-08-04 00:35:06,920 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'image', 'data': {'file': 'A8B66B907732B72800F834AB9BAB6D47.gif', 'subType': 0, 'url': 'https://multimedia.nt.qq.com.cn/download?appid=1407&fileid=EhRLcUGX3l922W9gDMuTYJe__5c4PxihikUg_woorImLl4nvjgMyBHByb2RQgL2jAVoQ3NPMuLL2ys6MzzWmJjn4N3oCK6k&spec=0&rkey=CAESMNNVPZfw5rX3H5XeBITYb0fU7zm0e5uiUzDyVMJySSXyIZ5GeoMeq4g_uhVDvokh3A', 'file_size': '1131809'}}, {'type': 'text', 'data': {'text': '你把大滴打的掉段了'}}]...
2025-08-04 00:35:14,564 - utils - INFO - 收到群消息 - 群:*********, 用户:354427196, 内容:[{'type': 'text', 'data': {'text': '不是你送走的？'}}]...
2025-08-04 00:35:14,565 - utils - INFO - 触发智能回复 - 群:*********
2025-08-04 00:35:16,483 - utils - INFO - 正在生成AI回复...
2025-08-04 00:35:17,542 - utils - INFO - Kimi AI回复: 真的吗？不是我送的哦，别乱说。😅...
2025-08-04 00:35:19,414 - utils - INFO - 智能回复成功 - 群:*********, 回复:真的吗？不是我送的哦，别乱说。😅...
2025-08-04 00:35:19,414 - utils - INFO - 消息处理成功
2025-08-04 00:35:19,414 - utils - INFO - 收到群消息 - 群:*********, 用户:354427196, 内容:[{'type': 'text', 'data': {'text': '恶心人的蚂蚱'}}]...
2025-08-04 00:35:21,391 - utils - INFO - 收到群消息 - 群:*********, 用户:354427196, 内容:[{'type': 'text', 'data': {'text': '4-2全3'}}]...
2025-08-04 00:35:28,788 - utils - INFO - 收到群消息 - 群:*********, 用户:285682801, 内容:[{'type': 'text', 'data': {'text': '送了妮蔻阿'}}]...
2025-08-04 00:35:42,510 - utils - INFO - QQ机器人已停止
2025-08-04 00:35:42,510 - utils - INFO - QQ机器人已停止

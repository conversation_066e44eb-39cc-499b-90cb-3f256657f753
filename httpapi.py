import requests

class LLOneBotClient:
    def __init__(self, base_url="http://127.0.0.1:3000"):
        self.base_url = base_url.rstrip("/")

    def _post(self, endpoint, data=None):
        url = f"{self.base_url}/{endpoint}"
        try:
            response = requests.post(url, json=data)
            return response.json()
        except requests.RequestException as e:
            return {"status": "error", "error": str(e)}

    def send_private_msg(self, user_id, message):
        return self._post("send_private_msg", {"user_id": user_id, "message": message})

    def send_group_msg(self, group_id, message):
        return self._post("send_group_msg", {"group_id": group_id, "message": message})

    def send_msg(self, message_type, id, message):
        return self._post("send_msg", {"message_type": message_type, "user_id" if message_type == "private" else "group_id": id, "message": message})

    def delete_msg(self, message_id):
        return self._post("delete_msg", {"message_id": message_id})

    def get_msg(self, message_id):
        return self._post("get_msg", {"message_id": message_id})

    def get_forward_msg(self, id):
        return self._post("get_forward_msg", {"id": id})

    def send_like(self, user_id, times=1):
        return self._post("send_like", {"user_id": user_id, "times": times})

    def set_group_kick(self, group_id, user_id, reject_add_request=False):
        return self._post("set_group_kick", {"group_id": group_id, "user_id": user_id, "reject_add_request": reject_add_request})

    def set_group_ban(self, group_id, user_id, duration=1800):
        return self._post("set_group_ban", {"group_id": group_id, "user_id": user_id, "duration": duration})

    def set_group_whole_ban(self, group_id, enable=True):
        return self._post("set_group_whole_ban", {"group_id": group_id, "enable": enable})

    def set_group_admin(self, group_id, user_id, enable=True):
        return self._post("set_group_admin", {"group_id": group_id, "user_id": user_id, "enable": enable})

    def set_group_card(self, group_id, user_id, card):
        return self._post("set_group_card", {"group_id": group_id, "user_id": user_id, "card": card})

    def set_group_name(self, group_id, group_name):
        return self._post("set_group_name", {"group_id": group_id, "group_name": group_name})

    def set_group_leave(self, group_id, is_dismiss=False):
        return self._post("set_group_leave", {"group_id": group_id, "is_dismiss": is_dismiss})

    def set_friend_add_request(self, flag, approve=True, remark=""):
        return self._post("set_friend_add_request", {"flag": flag, "approve": approve, "remark": remark})

    def set_group_add_request(self, flag, sub_type, approve=True, reason=""):
        return self._post("set_group_add_request", {"flag": flag, "sub_type": sub_type, "approve": approve, "reason": reason})

    def get_login_info(self):
        return self._post("get_login_info")

    def get_stranger_info(self, user_id, no_cache=False):
        return self._post("get_stranger_info", {"user_id": user_id, "no_cache": no_cache})

    def get_friend_list(self):
        return self._post("get_friend_list")

    def get_group_info(self, group_id, no_cache=False):
        return self._post("get_group_info", {"group_id": group_id, "no_cache": no_cache})

    def get_group_list(self):
        return self._post("get_group_list")

    def get_group_member_info(self, group_id, user_id, no_cache=False):
        return self._post("get_group_member_info", {"group_id": group_id, "user_id": user_id, "no_cache": no_cache})

    def get_group_member_list(self, group_id):
        return self._post("get_group_member_list", {"group_id": group_id})

    def get_group_honor_info(self, group_id, type="talkative"):
        return self._post("get_group_honor_info", {"group_id": group_id, "type": type})

    def get_cookies(self, domain=""):
        return self._post("get_cookies", {"domain": domain})

    def get_csrf_token(self):
        return self._post("get_csrf_token")

    def get_credentials(self, domain=""):
        return self._post("get_credentials", {"domain": domain})

    def get_record(self, file, out_format="mp3"):
        return self._post("get_record", {"file": file, "out_format": out_format})

    def get_image(self, file):
        return self._post("get_image", {"file": file})

    def can_send_image(self):
        return self._post("can_send_image")

    def can_send_record(self):
        return self._post("can_send_record")

    def get_status(self):
        return self._post("get_status")

    def get_version_info(self):
        return self._post("get_version_info")

    def set_restart(self):
        return self._post("set_restart")

    def clean_cache(self):
        return self._post("clean_cache")

    def set_group_special_title(self, group_id, user_id, special_title, duration=-1):
        return self._post("set_group_special_title", {
            "group_id": group_id,
            "user_id": user_id,
            "special_title": special_title,
            "duration": duration
        })

    '''
    *********************************************************************************
    '''

    def set_qq_profile(self, nickname=None, company=None, email=None, college=None, personal_note=None):
        return self._post("set_qq_profile", {
            "nickname": nickname,
            "company": company,
            "email": email,
            "college": college,
            "personal_note": personal_note
        })

    def delete_friend(self, user_id):
        return self._post("delete_friend", {"user_id": user_id})

    def mark_msg_as_read(self, message_id):
        return self._post("mark_msg_as_read", {"message_id": message_id})

    def send_group_forward_msg(self, messages, group_id):
        return self._post("send_group_forward_msg", {
            "group_id": group_id,
            "messages": messages
        })

    def send_private_forward_msg(self, messages, user_id):
        return self._post("send_private_forward_msg", {
            "user_id": user_id,
            "messages": messages
        })

    def get_group_msg_history(self, group_id, message_seq=None):
        data = {"group_id": group_id}
        if message_seq is not None:
            data["message_seq"] = message_seq
        return self._post("get_group_msg_history", data)

    def ocr_image(self, image):
        return self._post("ocr_image", {"image": image})

    def get_group_system_msg(self):
        return self._post("get_group_system_msg")

    def get_essence_msg_list(self, group_id):
        return self._post("get_essence_msg_list", {"group_id": group_id})

    def get_group_at_all_remain(self, group_id):
        return self._post("get_group_at_all_remain", {"group_id": group_id})

    def set_group_portrait(self, group_id, file, cache=1):
        return self._post("set_group_portrait", {
            "group_id": group_id,
            "file": file,
            "cache": cache
        })

    def set_essence_msg(self, message_id):
        return self._post("set_essence_msg", {"message_id": message_id})

    def delete_essence_msg(self, message_id):
        return self._post("delete_essence_msg", {"message_id": message_id})

    def send_group_notice(self, group_id, content, image=None):
        return self._post("_send_group_notice", {
            "group_id": group_id,
            "content": content,
            "image": image
        })

    def get_group_notice(self, group_id):
        return self._post("_get_group_notice", {"group_id": group_id})

    def upload_group_file(self, group_id, file, name, folder=None):
        return self._post("upload_group_file", {
            "group_id": group_id,
            "file": file,
            "name": name,
            "folder": folder
        })

    def delete_group_file(self, group_id, file_id, busid):
        return self._post("delete_group_file", {
            "group_id": group_id,
            "file_id": file_id,
            "busid": busid
        })

    def create_group_file_folder(self, group_id, name, parent_id=None):
        return self._post("create_group_file_folder", {
            "group_id": group_id,
            "name": name,
            "parent_id": parent_id
        })

    def delete_group_folder(self, group_id, folder_id):
        return self._post("delete_group_folder", {
            "group_id": group_id,
            "folder_id": folder_id
        })

    def get_group_file_system_info(self, group_id):
        return self._post("get_group_file_system_info", {"group_id": group_id})

    def get_group_root_files(self, group_id):
        return self._post("get_group_root_files", {"group_id": group_id})

    def get_group_files_by_folder(self, group_id, folder_id):
        return self._post("get_group_files_by_folder", {
            "group_id": group_id,
            "folder_id": folder_id
        })

    def get_group_file_url(self, group_id, file_id, busid):
        return self._post("get_group_file_url", {
            "group_id": group_id,
            "file_id": file_id,
            "busid": busid
        })

    def upload_private_file(self, user_id, file, name):
        return self._post("upload_private_file", {
            "user_id": user_id,
            "file": file,
            "name": name
        })

    def download_file(self, url, thread_count=3):
        return self._post("download_file", {
            "url": url,
            "thread_count": thread_count
        })

    def handle_quick_operation(self, context, operation):
        return self._post(".handle_quick_operation", {
            "context": context,
            "operation": operation
        })

"""
消息查询模块
"""
import time
from typing import List, Dict, Any, Optional, Generator
from httpAPI import LLOneBotClient
from utils import HumanLikeDelay, logger
from config import Config

class MessageQuery:
    """消息查询器"""
    
    def __init__(self, client: LLOneBotClient):
        self.client = client
        logger.info("消息查询器初始化完成")
    
    def get_group_messages(self, group_id: str, limit: int = 20, 
                          message_seq: int = None) -> List[Dict[str, Any]]:
        """
        获取群聊消息历史
        
        Args:
            group_id: 群号
            limit: 获取消息数量限制
            message_seq: 消息序号，用于分页
            
        Returns:
            消息列表
        """
        try:
            logger.info(f"查询群聊消息 - 群:{group_id}, 限制:{limit}")
            
            # 人性化延迟
            HumanLikeDelay.random_delay(1.0, 2.0)
            
            # 调用API获取消息历史
            result = self.client.get_group_msg_history(group_id, message_seq)
            
            if result.get("status") == "ok":
                messages = result.get("data", {}).get("messages", [])
                logger.info(f"成功获取 {len(messages)} 条群聊消息")
                return messages[:limit]  # 限制返回数量
            else:
                logger.error(f"获取群聊消息失败: {result}")
                return []
                
        except Exception as e:
            logger.error(f"查询群聊消息异常: {e}")
            return []
    
    def get_group_messages_paginated(self, group_id: str, 
                                   total_limit: int = 100) -> Generator[Dict[str, Any], None, None]:
        """
        分页获取群聊消息
        
        Args:
            group_id: 群号
            total_limit: 总消息数量限制
            
        Yields:
            消息数据
        """
        try:
            fetched_count = 0
            message_seq = None
            
            while fetched_count < total_limit:
                # 计算本次获取数量
                batch_size = min(20, total_limit - fetched_count)
                
                logger.debug(f"分页查询群聊消息 - 群:{group_id}, 已获取:{fetched_count}, 本次:{batch_size}")
                
                # 获取一批消息
                messages = self.get_group_messages(group_id, batch_size, message_seq)
                
                if not messages:
                    logger.info("没有更多消息，结束分页查询")
                    break
                
                # 返回消息
                for message in messages:
                    yield message
                    fetched_count += 1
                    
                    if fetched_count >= total_limit:
                        break
                
                # 更新消息序号用于下次查询
                if messages:
                    # 获取最后一条消息的序号
                    last_message = messages[-1]
                    message_seq = last_message.get("message_seq")
                
                # 如果获取的消息数量少于请求数量，说明没有更多消息了
                if len(messages) < batch_size:
                    logger.info("已获取所有可用消息")
                    break
                
                # 分页间隔延迟
                time.sleep(Config.MESSAGE_QUERY_DELAY)
                
        except Exception as e:
            logger.error(f"分页查询群聊消息异常: {e}")
    
    def search_group_messages(self, group_id: str, keyword: str, 
                            limit: int = 50) -> List[Dict[str, Any]]:
        """
        搜索群聊消息
        
        Args:
            group_id: 群号
            keyword: 搜索关键词
            limit: 搜索结果限制
            
        Returns:
            匹配的消息列表
        """
        try:
            logger.info(f"搜索群聊消息 - 群:{group_id}, 关键词:{keyword}")
            
            matched_messages = []
            
            # 分页搜索消息
            for message in self.get_group_messages_paginated(group_id, limit * 2):
                message_text = message.get("message", "")
                
                # 简单的关键词匹配
                if keyword.lower() in message_text.lower():
                    matched_messages.append(message)
                    
                    if len(matched_messages) >= limit:
                        break
            
            logger.info(f"搜索完成，找到 {len(matched_messages)} 条匹配消息")
            return matched_messages
            
        except Exception as e:
            logger.error(f"搜索群聊消息异常: {e}")
            return []
    
    def get_user_messages_in_group(self, group_id: str, user_id: str, 
                                 limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取特定用户在群内的消息
        
        Args:
            group_id: 群号
            user_id: 用户QQ号
            limit: 消息数量限制
            
        Returns:
            用户消息列表
        """
        try:
            logger.info(f"查询用户群聊消息 - 群:{group_id}, 用户:{user_id}")
            
            user_messages = []
            
            # 分页查询消息
            for message in self.get_group_messages_paginated(group_id, limit * 3):
                if str(message.get("user_id", "")) == str(user_id):
                    user_messages.append(message)
                    
                    if len(user_messages) >= limit:
                        break
            
            logger.info(f"找到用户 {user_id} 的 {len(user_messages)} 条消息")
            return user_messages
            
        except Exception as e:
            logger.error(f"查询用户群聊消息异常: {e}")
            return []
    
    def get_recent_group_activity(self, group_id: str, 
                                hours: int = 24) -> Dict[str, Any]:
        """
        获取群聊最近活动统计
        
        Args:
            group_id: 群号
            hours: 统计时间范围（小时）
            
        Returns:
            活动统计数据
        """
        try:
            logger.info(f"统计群聊活动 - 群:{group_id}, 时间范围:{hours}小时")
            
            current_time = int(time.time())
            time_threshold = current_time - (hours * 3600)
            
            # 统计数据
            stats = {
                "total_messages": 0,
                "active_users": set(),
                "message_types": {},
                "time_range": f"{hours}小时",
                "group_id": group_id
            }
            
            # 分页获取消息并统计
            for message in self.get_group_messages_paginated(group_id, 200):
                message_time = message.get("time", 0)
                
                # 检查时间范围
                if message_time < time_threshold:
                    continue
                
                stats["total_messages"] += 1
                stats["active_users"].add(str(message.get("user_id", "")))
                
                # 统计消息类型（简单分类）
                message_text = message.get("message", "")
                if "[CQ:image" in message_text:
                    stats["message_types"]["image"] = stats["message_types"].get("image", 0) + 1
                elif "[CQ:at" in message_text:
                    stats["message_types"]["at"] = stats["message_types"].get("at", 0) + 1
                else:
                    stats["message_types"]["text"] = stats["message_types"].get("text", 0) + 1
            
            # 转换set为数量
            stats["active_users"] = len(stats["active_users"])
            
            logger.info(f"群聊活动统计完成: {stats['total_messages']}条消息, {stats['active_users']}活跃用户")
            return stats
            
        except Exception as e:
            logger.error(f"统计群聊活动异常: {e}")
            return {}

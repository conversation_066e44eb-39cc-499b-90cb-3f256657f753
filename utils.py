"""
工具类模块
"""
import time
import random
import logging
import re
from typing import List
from config import Config

# 配置日志
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(Config.LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class HumanLikeDelay:
    """人性化延迟工具"""
    
    @staticmethod
    def random_delay(min_delay: float = None, max_delay: float = None):
        """随机延迟，模拟人类操作"""
        min_delay = min_delay or Config.MIN_DELAY
        max_delay = max_delay or Config.MAX_DELAY
        
        # 使用正态分布生成更自然的延迟
        mean = (min_delay + max_delay) / 2
        std = (max_delay - min_delay) / 6  # 3σ原则
        
        delay = random.normalvariate(mean, std)
        delay = max(min_delay, min(max_delay, delay))  # 限制在范围内
        
        logger.debug(f"延迟 {delay:.2f} 秒")
        time.sleep(delay)
    
    @staticmethod
    def typing_delay(text: str, wpm: int = 120):
        """根据文本长度模拟打字延迟"""
        # 假设平均每分钟打字数
        chars_per_second = (wpm * 5) / 60  # 假设平均每个词5个字符
        typing_time = len(text) / chars_per_second
        
        # 添加一些随机性
        actual_delay = typing_time * random.uniform(0.8, 1.2)
        actual_delay = max(0.5, min(5.0, actual_delay))  # 限制在合理范围
        
        logger.debug(f"模拟打字延迟 {actual_delay:.2f} 秒")
        time.sleep(actual_delay)

class MessageParser:
    """消息解析工具"""
    
    @staticmethod
    def extract_at_users(message: str) -> List[str]:
        """提取消息中@的用户QQ号"""
        # OneBot格式: [CQ:at,qq=123456]
        pattern = r'\[CQ:at,qq=(\d+)\]'
        return re.findall(pattern, message)
    
    @staticmethod
    def is_at_bot(message: str, bot_qq: str) -> bool:
        """检查消息是否@了机器人"""
        at_users = MessageParser.extract_at_users(message)
        return bot_qq in at_users
    
    @staticmethod
    def clean_message(message: str) -> str:
        """清理消息，移除CQ码等"""
        # 移除所有CQ码
        cleaned = re.sub(r'\[CQ:[^\]]+\]', '', message)
        return cleaned.strip()
    
    @staticmethod
    def truncate_message(message: str, max_length: int = None) -> str:
        """截断过长的消息"""
        max_length = max_length or Config.MAX_MESSAGE_LENGTH
        if len(message) <= max_length:
            return message
        return message[:max_length-3] + "..."

class ResponseFormatter:
    """回复格式化工具"""
    
    @staticmethod
    def format_at_reply(user_qq: str, content: str) -> str:
        """格式化@回复"""
        return f"[CQ:at,qq={user_qq}] {content}"
    


class SafetyChecker:
    """安全检查工具"""
    
    @staticmethod
    def is_safe_message(message: str) -> bool:
        """检查消息是否安全（简单的关键词过滤）"""
        # 这里可以添加敏感词过滤逻辑
        dangerous_keywords = ["删除", "踢出", "禁言", "解散"]
        message_lower = message.lower()
        
        for keyword in dangerous_keywords:
            if keyword in message_lower:
                logger.warning(f"检测到潜在危险操作: {keyword}")
                return False
        return True
    
    @staticmethod
    def validate_qq_number(qq: str) -> bool:
        """验证QQ号格式"""
        return qq.isdigit() and 5 <= len(qq) <= 11

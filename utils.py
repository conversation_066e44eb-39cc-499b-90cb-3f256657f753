"""
工具类模块
"""
import time
import random
import logging
import re
from typing import List
from config import Config

# 配置日志
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(Config.LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class HumanLikeDelay:
    """人性化延迟工具"""
    
    @staticmethod
    def random_delay(min_delay: float = None, max_delay: float = None):
        """随机延迟，模拟人类操作"""
        min_delay = min_delay or Config.MIN_DELAY
        max_delay = max_delay or Config.MAX_DELAY
        
        # 使用正态分布生成更自然的延迟
        mean = (min_delay + max_delay) / 2
        std = (max_delay - min_delay) / 6  # 3σ原则
        
        delay = random.normalvariate(mean, std)
        delay = max(min_delay, min(max_delay, delay))  # 限制在范围内
        
        logger.debug(f"延迟 {delay:.2f} 秒")
        time.sleep(delay)
    
    @staticmethod
    def typing_delay(text: str, wpm: int = 120):
        """根据文本长度模拟打字延迟"""
        # 假设平均每分钟打字数
        chars_per_second = (wpm * 5) / 60  # 假设平均每个词5个字符
        typing_time = len(text) / chars_per_second
        
        # 添加一些随机性
        actual_delay = typing_time * random.uniform(0.8, 1.2)
        actual_delay = max(0.5, min(5.0, actual_delay))  # 限制在合理范围
        
        logger.debug(f"模拟打字延迟 {actual_delay:.2f} 秒")
        time.sleep(actual_delay)

class MessageParser:
    """消息解析工具"""

    @staticmethod
    def parse_message_content(message) -> str:
        """解析消息内容，支持数组和字符串格式"""
        if isinstance(message, str):
            return message
        elif isinstance(message, list):
            # LLOneBot格式: [{'type': 'text', 'data': {'text': 'content'}}]
            text_parts = []
            for part in message:
                if isinstance(part, dict):
                    if part.get('type') == 'text':
                        text_data = part.get('data', {})
                        text_content = text_data.get('text', '')
                        text_parts.append(text_content)
                    elif part.get('type') == 'at':
                        # @消息格式
                        at_data = part.get('data', {})
                        qq = at_data.get('qq', '')
                        text_parts.append(f'[CQ:at,qq={qq}]')
            return ''.join(text_parts)
        else:
            return str(message)

    @staticmethod
    def extract_at_users(message) -> List[str]:
        """提取消息中@的用户QQ号"""
        # 先解析消息内容
        message_str = MessageParser.parse_message_content(message)

        # 处理数组格式中的@
        if isinstance(message, list):
            at_users = []
            for part in message:
                if isinstance(part, dict) and part.get('type') == 'at':
                    at_data = part.get('data', {})
                    qq = str(at_data.get('qq', ''))
                    if qq:
                        at_users.append(qq)
            if at_users:
                return at_users

        # OneBot格式: [CQ:at,qq=123456]
        pattern = r'\[CQ:at,qq=(\d+)\]'
        return re.findall(pattern, message_str)

    @staticmethod
    def is_at_bot(message, bot_qq: str) -> bool:
        """检查消息是否@了机器人"""
        at_users = MessageParser.extract_at_users(message)
        return bot_qq in at_users

    @staticmethod
    def clean_message(message) -> str:
        """清理消息，移除CQ码等"""
        # 先解析消息内容
        message_str = MessageParser.parse_message_content(message)
        # 移除所有CQ码
        cleaned = re.sub(r'\[CQ:[^\]]+\]', '', message_str)
        return cleaned.strip()
    
    @staticmethod
    def truncate_message(message, max_length: int = None) -> str:
        """截断过长的消息"""
        # 先解析消息内容
        message_str = MessageParser.parse_message_content(message)
        max_length = max_length or Config.MAX_MESSAGE_LENGTH
        if len(message_str) <= max_length:
            return message_str
        return message_str[:max_length-3] + "..."

class ResponseFormatter:
    """回复格式化工具"""
    
    @staticmethod
    def format_at_reply(user_qq: str, content: str) -> str:
        """格式化@回复"""
        return f"[CQ:at,qq={user_qq}] {content}"
    


class SafetyChecker:
    """安全检查工具"""
    
    @staticmethod
    def is_safe_message(message) -> bool:
        """检查消息是否安全（简单的关键词过滤）"""
        # 先解析消息内容
        from utils import MessageParser
        message_str = MessageParser.parse_message_content(message)

        # 这里可以添加敏感词过滤逻辑
        # dangerous_keywords = ["删除", "踢出", "禁言", "解散"]
        dangerous_keywords = []
        message_lower = message_str.lower()
        
        for keyword in dangerous_keywords:
            if keyword in message_lower:
                logger.warning(f"检测到潜在危险操作: {keyword}")
                return False
        return True
    
    @staticmethod
    def validate_qq_number(qq: str) -> bool:
        """验证QQ号格式"""
        return qq.isdigit() and 5 <= len(qq) <= 11

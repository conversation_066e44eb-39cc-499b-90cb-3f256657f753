"""
QQ机器人使用示例
"""
import time
import signal
import sys
from common import QQBot
from config import Config

class QQBotExample:
    """QQ机器人使用示例"""
    
    def __init__(self):
        self.bot = QQBot()
        self.running = False
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print("\n收到停止信号，正在关闭机器人...")
        self.running = False
        self.bot.stop()
        sys.exit(0)
    
    def setup_message_callbacks(self):
        """设置消息回调"""
        def log_message(message_data):
            """记录所有消息"""
            msg_type = message_data.get("message_type", "unknown")
            user_id = message_data.get("user_id", "unknown")
            message = message_data.get("message", "")[:50]
            print(f"[消息日志] {msg_type} - {user_id}: {message}...")
        
        # 添加消息回调
        self.bot.message_handler.add_message_callback(log_message)
    
    def run_interactive_mode(self):
        """运行交互模式"""
        print("=== QQ机器人交互模式 ===")
        print("输入命令来测试机器人功能，输入 'help' 查看帮助，输入 'quit' 退出")
        
        while self.running:
            try:
                command = input("\n> ").strip().lower()
                
                if command == "quit":
                    break
                elif command == "help":
                    self.show_help()
                elif command == "status":
                    self.show_status()
                elif command == "groups":
                    self.list_groups()
                elif command == "friends":
                    self.list_friends()
                elif command.startswith("group_info "):
                    group_id = command.split(" ", 1)[1]
                    self.show_group_info(group_id)
                elif command.startswith("group_members "):
                    group_id = command.split(" ", 1)[1]
                    self.show_group_members(group_id)
                elif command.startswith("send_group "):
                    parts = command.split(" ", 2)
                    if len(parts) >= 3:
                        group_id, message = parts[1], parts[2]
                        self.send_group_message(group_id, message)
                    else:
                        print("用法: send_group <群号> <消息内容>")
                elif command.startswith("query_messages "):
                    group_id = command.split(" ", 1)[1]
                    self.query_group_messages(group_id)
                elif command == "test_ai":
                    self.test_ai_reply()
                else:
                    print(f"未知命令: {command}，输入 'help' 查看帮助")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"执行命令时出错: {e}")
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
可用命令:
  help                    - 显示此帮助信息
  status                  - 显示机器人状态
  groups                  - 列出所有群
  friends                 - 列出所有好友
  group_info <群号>       - 显示群信息
  group_members <群号>    - 显示群成员
  send_group <群号> <消息> - 发送群消息
  query_messages <群号>   - 查询群消息历史
  test_ai                 - 测试AI回复
  quit                    - 退出程序
        """
        print(help_text)
    
    def show_status(self):
        """显示机器人状态"""
        status = self.bot.get_status()
        print(f"机器人状态:")
        print(f"  运行中: {status['is_running']}")
        print(f"  AI可用: {status['ai_available']}")
        if status['bot_info']:
            print(f"  机器人昵称: {status['bot_info'].get('nickname', 'Unknown')}")
            print(f"  机器人QQ: {status['bot_info'].get('user_id', 'Unknown')}")
    
    def list_groups(self):
        """列出群列表"""
        groups = self.bot.operations.get_group_list()
        if groups:
            print(f"群列表 (共{len(groups)}个):")
            for group in groups[:10]:  # 只显示前10个
                print(f"  {group.get('group_id', 'Unknown')} - {group.get('group_name', 'Unknown')}")
            if len(groups) > 10:
                print(f"  ... 还有 {len(groups) - 10} 个群")
        else:
            print("未获取到群列表")
    
    def list_friends(self):
        """列出好友列表"""
        friends = self.bot.operations.get_friend_list()
        if friends:
            print(f"好友列表 (共{len(friends)}个):")
            for friend in friends[:10]:  # 只显示前10个
                print(f"  {friend.get('user_id', 'Unknown')} - {friend.get('nickname', 'Unknown')}")
            if len(friends) > 10:
                print(f"  ... 还有 {len(friends) - 10} 个好友")
        else:
            print("未获取到好友列表")
    
    def show_group_info(self, group_id):
        """显示群信息"""
        group_info = self.bot.operations.get_group_info(group_id)
        if group_info:
            print(f"群信息:")
            print(f"  群号: {group_info.get('group_id', 'Unknown')}")
            print(f"  群名: {group_info.get('group_name', 'Unknown')}")
            print(f"  成员数: {group_info.get('member_count', 'Unknown')}")
            print(f"  最大成员数: {group_info.get('max_member_count', 'Unknown')}")
        else:
            print(f"未获取到群 {group_id} 的信息")
    
    def show_group_members(self, group_id):
        """显示群成员"""
        members = self.bot.operations.get_group_members(group_id)
        if members:
            print(f"群 {group_id} 成员列表 (共{len(members)}人):")
            for member in members[:5]:  # 只显示前5个
                print(f"  {member.get('user_id', 'Unknown')} - {member.get('nickname', 'Unknown')}")
            if len(members) > 5:
                print(f"  ... 还有 {len(members) - 5} 个成员")
        else:
            print(f"未获取到群 {group_id} 的成员列表")
    
    def send_group_message(self, group_id, message):
        """发送群消息"""
        result = self.bot.client.send_group_msg(group_id, message)
        if result.get("status") == "ok":
            print(f"消息发送成功到群 {group_id}")
        else:
            print(f"消息发送失败: {result}")
    
    def query_group_messages(self, group_id):
        """查询群消息"""
        print(f"正在查询群 {group_id} 的消息历史...")
        messages = self.bot.message_query.get_group_messages(group_id, 5)
        if messages:
            print(f"最近 {len(messages)} 条消息:")
            for msg in messages:
                user_id = msg.get("sender", {}).get("user_id", "Unknown")
                nickname = msg.get("sender", {}).get("nickname", "Unknown")
                content = msg.get("message", "")[:50]
                print(f"  {nickname}({user_id}): {content}...")
        else:
            print("未获取到消息历史")
    
    def test_ai_reply(self):
        """测试AI回复"""
        if not self.bot.ai:
            print("AI接口未初始化")
            return
        
        test_message = "你好，请介绍一下自己"
        print(f"测试消息: {test_message}")
        reply = self.bot.ai.get_qq_reply(test_message)
        if reply:
            print(f"AI回复: {reply}")
        else:
            print("AI回复失败")
    
    def run(self):
        """运行示例"""
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # 启动机器人
        if not self.bot.start():
            print("机器人启动失败")
            return
        
        self.running = True
        
        # 设置消息回调
        self.setup_message_callbacks()
        
        print(f"QQ机器人已启动，机器人QQ: {Config.BOT_QQ}")
        print("注意: 这是一个示例程序，实际使用时需要配置消息接收服务")
        
        # 运行交互模式
        self.run_interactive_mode()
        
        # 停止机器人
        self.bot.stop()
        print("机器人已停止")

def main():
    """主函数"""
    print("QQ机器人使用示例")
    print("请确保已正确配置环境变量或修改config.py中的配置")
    
    example = QQBotExample()
    example.run()

if __name__ == "__main__":
    main()

import httpAPI




import websocket
import threading
import json
import time
import random
from httpAPI import LLOneBotClient

# 实例化客户端，确保 LLOneBot 服务已运行，http 地址正确
client = LLOneBotClient("http://127.0.0.1:3000")
WS_URL = "ws://127.0.0.1:3001"  # WebSocket 地址

# 一些早餐消息示例
breakfast_messages = [
    '吃饭中'
]

# 收到消息时处理逻辑
def on_message(ws, message):
    try:
        data = json.loads(message)
        if data.get("post_type") == "message" and data.get("message_type") == "group":
            group_id = data["group_id"]
            msg = data["message"]  # 是一个列表
            self_id = str(data["self_id"])  # 有时候是 int，要转成 str 再比较

            print(f"[消息] 群: {group_id}, 内容: {msg}")

            # 判断是否 @ 了机器人
            at_bot = any(
                segment["type"] == "at" and str(segment["data"].get("qq")) == self_id
                for segment in msg
            )

            if at_bot:
                time.sleep(1 + random.gauss(0, 1))
                reply = random.choice(breakfast_messages)
                client.send_group_msg(group_id, reply)
    except Exception as e:
        print("[消息处理异常]", e)


def on_open(ws):
    print("✅ WebSocket 连接成功，开始监听消息...")

def on_close(ws, code, reason):
    print(f"❌ WebSocket 关闭: {code}, 原因: {reason}")

def on_error(ws, error):
    print(f"🔥 WebSocket 错误: {error}")

def start_websocket():
    while True:
        try:
            ws = websocket.WebSocketApp(
                WS_URL,
                on_message=on_message,
                on_open=on_open,
                on_close=on_close,
                on_error=on_error
            )
            ws.run_forever()
        except Exception as e:
            print("[连接异常]", e)
        print("⏳ 5 秒后重连...")
        time.sleep(5)

if __name__ == "__main__":
    threading.Thread(target=start_websocket, daemon=True).start()
    while True:
        time.sleep(3)



"""
AI接口模块 - 集成Kimi API
"""
import requests
import json
import logging
from typing import Optional, Dict, Any
from config import Config
from utils import logger

class KimiAI:
    """Kimi AI接口类"""
    
    def __init__(self, api_key: str = None, base_url: str = None, model: str = None):
        self.api_key = api_key or Config.KIMI_API_KEY
        self.base_url = base_url or Config.KIMI_BASE_URL
        self.model = model or Config.KIMI_MODEL
        
        if not self.api_key:
            raise ValueError("Kimi API key is required")
        
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        logger.info(f"Kimi AI initialized with model: {self.model}")
    
    def chat(self, message: str, system_prompt: str = None) -> Optional[str]:
        """
        与Kimi AI对话
        
        Args:
            message: 用户消息
            system_prompt: 系统提示词
            
        Returns:
            AI回复内容，失败时返回None
        """
        try:
            # 构建消息
            messages = []
            
            if system_prompt:
                messages.append({
                    "role": "system",
                    "content": system_prompt
                })
            
            messages.append({
                "role": "user", 
                "content": message
            })
            
            # 请求数据
            data = {
                "model": self.model,
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 1000
            }
            
            logger.debug(f"发送请求到Kimi API: {message[:50]}...")
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                reply = result["choices"][0]["message"]["content"]
                logger.info(f"Kimi AI回复: {reply[:50]}...")
                return reply.strip()
            else:
                logger.error(f"Kimi API请求失败: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            logger.error("Kimi API请求超时")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"Kimi API请求异常: {e}")
            return None
        except Exception as e:
            logger.error(f"Kimi AI处理异常: {e}")
            return None
    
    def get_qq_reply(self, user_message: str, user_qq: str = None, group_id: str = None) -> Optional[str]:
        """
        获取QQ群聊回复
        
        Args:
            user_message: 用户消息
            user_qq: 用户QQ号
            group_id: 群号
            
        Returns:
            回复内容
        """
        # 构建系统提示词
        system_prompt = """你是一个友好的QQ群聊机器人助手。请遵循以下规则：
1. 回复要简洁明了，不要太长
2. 语气要轻松友好，符合QQ群聊氛围
3. 可以使用一些网络用语和表情
4. 如果不确定如何回答，可以说"让我想想"或类似的话
5. 不要回复敏感或不当内容
6. 回复长度控制在100字以内"""
        
        # 清理用户消息（移除@等）
        from utils import MessageParser
        clean_message = MessageParser.clean_message(user_message)
        
        if not clean_message.strip():
            return "你好呀~ 有什么可以帮助你的吗？"
        
        # 获取AI回复
        reply = self.chat(clean_message, system_prompt)
        
        if reply:
            # 截断过长的回复
            from utils import MessageParser
            reply = MessageParser.truncate_message(reply, 200)
            return reply
        else:
            # AI服务不可用时的备用回复
            fallback_replies = [
                "我现在有点忙，稍后再聊~",
                "让我想想怎么回答你...",
                "网络有点卡，请稍后再试",
                "emmm...这个问题有点难呢"
            ]
            import random
            return random.choice(fallback_replies)
    
    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            reply = self.chat("你好", "请简单回复'你好'")
            return reply is not None
        except Exception as e:
            logger.error(f"Kimi API连接测试失败: {e}")
            return False

"""
QQ机器人主程序
"""
from common import QQBot
from utils import logger

def main():
    """主函数"""
    print("QQ机器人启动中...")
    
    # 创建机器人
    bot = QQBot()
    
    # 启动机器人
    if not bot.start():
        print("机器人启动失败")
        return
    
    # 显示当前配置
    status = bot.get_status()
    reply_settings = status.get("reply_settings", {})
    
    print("=" * 50)
    print("QQ机器人已启动")
    print(f"机器人QQ: {status['bot_info'].get('user_id', 'Unknown')}")
    print(f"机器人昵称: {status['bot_info'].get('nickname', 'Unknown')}")
    print()
    print("回复功能状态:")
    print(f"  @回复: {'✓' if reply_settings.get('at_reply') else '✗'}")
    print(f"  私聊回复: {'✓' if reply_settings.get('private_reply') else '✗'}")
    print(f"  智能回复: {'✓' if reply_settings.get('smart_reply') else '✗'}")
    print(f"  智能回复群: {reply_settings.get('smart_reply_groups', [])}")
    print(f"  智能回复概率: {reply_settings.get('smart_reply_probability', 0)}")
    print("=" * 50)
    
    print("\n机器人正在运行...")
    print("请配置OneBot服务将消息发送到此程序")
    print("消息处理流程: 接收消息 → Kimi AI → 直接回复")
    
    try:
        # 在这里你需要添加消息接收逻辑
        # 例如HTTP服务器或WebSocket连接
        # 当接收到消息时，调用: bot.process_message(message_data)
        
        input("\n按回车键停止机器人...")
        
    except KeyboardInterrupt:
        print("\n收到停止信号...")
    
    # 停止机器人
    bot.stop()
    print("机器人已停止")

if __name__ == "__main__":
    main()

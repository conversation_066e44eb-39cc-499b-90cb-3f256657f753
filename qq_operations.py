"""
QQ常用操作模块
"""
import random
from typing import List, Dict, Any, Optional
from httpAPI import LLOneBotClient
from utils import HumanLikeDelay, SafetyChecker, logger
from config import Config

class QQOperations:
    """QQ常用操作类"""
    
    def __init__(self, client: LLOneBotClient):
        self.client = client
        logger.info("QQ操作模块初始化完成")
    
    def get_bot_info(self) -> Dict[str, Any]:
        """获取机器人信息"""
        try:
            HumanLikeDelay.random_delay(0.5, 1.0)
            result = self.client.get_login_info()
            
            if result.get("status") == "ok":
                bot_info = result.get("data", {})
                logger.info(f"机器人信息: {bot_info.get('nickname', 'Unknown')} ({bot_info.get('user_id', 'Unknown')})")
                return bot_info
            else:
                logger.error(f"获取机器人信息失败: {result}")
                return {}
                
        except Exception as e:
            logger.error(f"获取机器人信息异常: {e}")
            return {}
    
    def get_group_list(self) -> List[Dict[str, Any]]:
        """获取群列表"""
        try:
            HumanLikeDelay.random_delay()
            result = self.client.get_group_list()
            
            if result.get("status") == "ok":
                groups = result.get("data", [])
                logger.info(f"获取到 {len(groups)} 个群")
                return groups
            else:
                logger.error(f"获取群列表失败: {result}")
                return []
                
        except Exception as e:
            logger.error(f"获取群列表异常: {e}")
            return []
    
    def get_friend_list(self) -> List[Dict[str, Any]]:
        """获取好友列表"""
        try:
            HumanLikeDelay.random_delay()
            result = self.client.get_friend_list()
            
            if result.get("status") == "ok":
                friends = result.get("data", [])
                logger.info(f"获取到 {len(friends)} 个好友")
                return friends
            else:
                logger.error(f"获取好友列表失败: {result}")
                return []
                
        except Exception as e:
            logger.error(f"获取好友列表异常: {e}")
            return []
    
    def get_group_members(self, group_id: str) -> List[Dict[str, Any]]:
        """获取群成员列表"""
        try:
            if not SafetyChecker.validate_qq_number(group_id):
                logger.error(f"无效的群号: {group_id}")
                return []
            
            HumanLikeDelay.random_delay()
            result = self.client.get_group_member_list(group_id)
            
            if result.get("status") == "ok":
                members = result.get("data", [])
                logger.info(f"群 {group_id} 有 {len(members)} 个成员")
                return members
            else:
                logger.error(f"获取群成员列表失败: {result}")
                return []
                
        except Exception as e:
            logger.error(f"获取群成员列表异常: {e}")
            return []
    
    def send_like(self, user_id: str, times: int = 1) -> bool:
        """给用户点赞"""
        try:
            if not SafetyChecker.validate_qq_number(user_id):
                logger.error(f"无效的QQ号: {user_id}")
                return False
            
            times = max(1, min(10, times))  # 限制点赞次数
            
            logger.info(f"给用户 {user_id} 点赞 {times} 次")
            HumanLikeDelay.random_delay()
            
            result = self.client.send_like(user_id, times)
            
            if result.get("status") == "ok":
                logger.info(f"点赞成功")
                return True
            else:
                logger.error(f"点赞失败: {result}")
                return False
                
        except Exception as e:
            logger.error(f"点赞异常: {e}")
            return False
    
    def get_group_info(self, group_id: str) -> Dict[str, Any]:
        """获取群信息"""
        try:
            if not SafetyChecker.validate_qq_number(group_id):
                logger.error(f"无效的群号: {group_id}")
                return {}
            
            HumanLikeDelay.random_delay()
            result = self.client.get_group_info(group_id)
            
            if result.get("status") == "ok":
                group_info = result.get("data", {})
                logger.info(f"群信息: {group_info.get('group_name', 'Unknown')} ({group_id})")
                return group_info
            else:
                logger.error(f"获取群信息失败: {result}")
                return {}
                
        except Exception as e:
            logger.error(f"获取群信息异常: {e}")
            return {}
    
    def get_user_info(self, user_id: str) -> Dict[str, Any]:
        """获取用户信息"""
        try:
            if not SafetyChecker.validate_qq_number(user_id):
                logger.error(f"无效的QQ号: {user_id}")
                return {}
            
            HumanLikeDelay.random_delay()
            result = self.client.get_stranger_info(user_id)
            
            if result.get("status") == "ok":
                user_info = result.get("data", {})
                logger.info(f"用户信息: {user_info.get('nickname', 'Unknown')} ({user_id})")
                return user_info
            else:
                logger.error(f"获取用户信息失败: {result}")
                return {}
                
        except Exception as e:
            logger.error(f"获取用户信息异常: {e}")
            return {}
    
    def send_random_emoji(self, group_id: str) -> bool:
        """发送随机表情到群"""
        try:
            if not SafetyChecker.validate_qq_number(group_id):
                logger.error(f"无效的群号: {group_id}")
                return False
            
            # 一些常用的表情
            emojis = ["😊", "😄", "🤔", "👍", "❤️", "😂", "🎉", "✨", "🌟", "💫"]
            emoji = random.choice(emojis)
            
            HumanLikeDelay.random_delay()
            result = self.client.send_group_msg(group_id, emoji)
            
            if result.get("status") == "ok":
                logger.info(f"发送表情成功: {emoji}")
                return True
            else:
                logger.error(f"发送表情失败: {result}")
                return False
                
        except Exception as e:
            logger.error(f"发送表情异常: {e}")
            return False
    
    def check_group_activity(self, group_id: str) -> Dict[str, Any]:
        """检查群活跃度"""
        try:
            if not SafetyChecker.validate_qq_number(group_id):
                logger.error(f"无效的群号: {group_id}")
                return {}
            
            # 获取群信息
            group_info = self.get_group_info(group_id)
            members = self.get_group_members(group_id)
            
            activity_info = {
                "group_id": group_id,
                "group_name": group_info.get("group_name", "Unknown"),
                "member_count": len(members),
                "max_member_count": group_info.get("max_member_count", 0),
                "activity_level": "unknown"
            }
            
            # 简单的活跃度判断
            if len(members) > 100:
                activity_info["activity_level"] = "high"
            elif len(members) > 20:
                activity_info["activity_level"] = "medium"
            else:
                activity_info["activity_level"] = "low"
            
            logger.info(f"群活跃度检查完成: {activity_info}")
            return activity_info
            
        except Exception as e:
            logger.error(f"检查群活跃度异常: {e}")
            return {}
    
    def batch_send_message(self, targets: List[Dict[str, str]], 
                          message: str, delay_range: tuple = (2, 5)) -> Dict[str, int]:
        """
        批量发送消息
        
        Args:
            targets: 目标列表，格式: [{"type": "group/private", "id": "123456"}]
            message: 消息内容
            delay_range: 延迟范围（秒）
            
        Returns:
            发送结果统计
        """
        try:
            results = {"success": 0, "failed": 0, "total": len(targets)}
            
            logger.info(f"开始批量发送消息，目标数量: {len(targets)}")
            
            for i, target in enumerate(targets):
                target_type = target.get("type", "")
                target_id = target.get("id", "")
                
                if not SafetyChecker.validate_qq_number(target_id):
                    logger.warning(f"跳过无效目标: {target}")
                    results["failed"] += 1
                    continue
                
                # 人性化延迟
                if i > 0:  # 第一个不延迟
                    HumanLikeDelay.random_delay(delay_range[0], delay_range[1])
                
                # 发送消息
                if target_type == "group":
                    result = self.client.send_group_msg(target_id, message)
                elif target_type == "private":
                    result = self.client.send_private_msg(target_id, message)
                else:
                    logger.warning(f"未知目标类型: {target_type}")
                    results["failed"] += 1
                    continue
                
                if result.get("status") == "ok":
                    results["success"] += 1
                    logger.debug(f"消息发送成功: {target_type}:{target_id}")
                else:
                    results["failed"] += 1
                    logger.warning(f"消息发送失败: {target_type}:{target_id} - {result}")
            
            logger.info(f"批量发送完成: 成功{results['success']}, 失败{results['failed']}")
            return results
            
        except Exception as e:
            logger.error(f"批量发送消息异常: {e}")
            return {"success": 0, "failed": len(targets), "total": len(targets)}

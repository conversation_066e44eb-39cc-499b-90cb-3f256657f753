"""
QQ机器人WebSocket版本 - 使用websocket-client连接LLOneBot
"""
import json
import time
import threading
import signal
import sys
import websocket
from common import QQBot
from utils import logger

class QQBotWebSocket:
    """QQ机器人WebSocket客户端"""

    def __init__(self, bot: QQBot, ws_url: str = "ws://127.0.0.1:3001"):
        self.bot = bot
        self.ws_url = ws_url
        self.running = False
        self.ws = None
        self.thread = None
        self.processed_messages = set()  # 用于去重的消息ID集合
    
    def on_message(self, ws, message):
        """收到消息时的回调"""
        try:
            # 解析消息
            data = json.loads(message)
            self.handle_message(data)
            
        except json.JSONDecodeError:
            logger.warning(f"无法解析消息: {message}")
        except Exception as e:
            logger.error(f"处理消息异常: {e}")
    
    def on_error(self, ws, error):
        """WebSocket错误回调"""
        logger.error(f"WebSocket错误: {error}")
    
    def on_close(self, ws, close_status_code, close_msg):
        """WebSocket关闭回调"""
        print("WebSocket连接已关闭")
        self.running = False
    
    def on_open(self, ws):
        """WebSocket连接成功回调"""
        print("✓ WebSocket连接成功，开始监听消息...")
        self.running = True
    
    def handle_message(self, data):
        """处理接收到的消息"""
        try:
            # 只处理消息事件
            if data.get("post_type") == "message":
                # 获取消息ID用于去重
                message_id = data.get("message_id")
                if message_id in self.processed_messages:
                    # 跳过重复消息
                    return

                # 添加到已处理消息集合
                self.processed_messages.add(message_id)

                # 限制集合大小，避免内存泄漏
                if len(self.processed_messages) > 1000:
                    # 移除最旧的500个消息ID
                    old_messages = list(self.processed_messages)[:500]
                    for old_id in old_messages:
                        self.processed_messages.discard(old_id)

                message_type = data.get("message_type")
                user_id = data.get("user_id")
                message = data.get("message", "")

                # 记录接收到的消息
                if message_type == "group":
                    group_id = data.get("group_id")
                    logger.info(f"收到群消息 - 群:{group_id}, 用户:{user_id}, 内容:{str(message)[:50]}...")
                elif message_type == "private":
                    logger.info(f"收到私聊消息 - 用户:{user_id}, 内容:{str(message)[:50]}...")

                # 使用机器人处理消息
                success = self.bot.process_message(data)

                if success:
                    logger.info("消息处理成功")
                else:
                    logger.debug("消息未处理")

        except Exception as e:
            logger.error(f"处理消息异常: {e}")
    
    def connect_and_listen(self):
        """连接WebSocket并监听消息"""
        try:
            print(f"正在连接到 {self.ws_url}...")
            
            # 创建WebSocket连接
            self.ws = websocket.WebSocketApp(
                self.ws_url,
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close,
                on_open=self.on_open
            )
            
            # 在单独线程中运行
            self.thread = threading.Thread(target=self.ws.run_forever)
            self.thread.daemon = True
            self.thread.start()
            
            # 等待连接建立
            time.sleep(2)
            
            if self.running:
                return True
            else:
                print("WebSocket连接失败")
                print("请确保LLOneBot正在运行并且WebSocket端口为3001")
                return False
                
        except Exception as e:
            print(f"WebSocket连接异常: {e}")
            return False
    
    def stop(self):
        """停止监听"""
        self.running = False
        if self.ws:
            self.ws.close()
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)

def main():
    """主函数"""
    print("QQ机器人WebSocket版本启动中...")
    
    # 创建机器人
    bot = QQBot()
    
    # 启动机器人
    if not bot.start():
        print("机器人启动失败")
        return
    
    # 显示当前配置
    status = bot.get_status()
    reply_settings = status.get("reply_settings", {})
    
    print("=" * 50)
    print("QQ机器人已启动")
    print(f"机器人QQ: {status['bot_info'].get('user_id', 'Unknown')}")
    print(f"机器人昵称: {status['bot_info'].get('nickname', 'Unknown')}")
    print()
    print("回复功能状态:")
    print(f"  @回复: {'✓' if reply_settings.get('at_reply') else '✗'}")
    print(f"  私聊回复: {'✓' if reply_settings.get('private_reply') else '✗'}")
    print(f"  智能回复: {'✓' if reply_settings.get('smart_reply') else '✗'}")
    print(f"  智能回复群: {reply_settings.get('smart_reply_groups', [])}")
    print(f"  智能回复概率: {reply_settings.get('smart_reply_probability', 0)}")
    print("=" * 50)
    
    # 创建WebSocket客户端
    ws_client = QQBotWebSocket(bot)
    
    # 设置信号处理
    def signal_handler(signum, frame):
        print("\n收到停止信号，正在关闭...")
        ws_client.stop()
        bot.stop()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 连接WebSocket
    if not ws_client.connect_and_listen():
        print("WebSocket连接失败，程序退出")
        bot.stop()
        return
    
    try:
        print("\n机器人正在运行，按Ctrl+C停止...")
        # 保持程序运行
        while ws_client.running:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n收到停止信号...")
    finally:
        ws_client.stop()
        bot.stop()
        print("机器人已停止")

if __name__ == "__main__":
    # 检查是否安装了websocket-client
    try:
        import websocket
    except ImportError:
        print("请先安装websocket-client库: pip install websocket-client")
        sys.exit(1)
    
    main()

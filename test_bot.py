"""
QQ机器人测试用例
"""
import os
import sys
from common import QQBot
from httpAPI import LLOneBotClient
from config import Config

def test_basic_functionality():
    """测试基本功能"""
    print("=== QQ机器人基本功能测试 ===")
    
    # 创建机器人实例
    try:
        bot = QQBot()
        print("✓ 机器人实例创建成功")
    except Exception as e:
        print(f"✗ 机器人实例创建失败: {e}")
        return False
    
    # 测试配置
    config = Config.get_config()
    print(f"✓ 配置加载成功，OneBot URL: {config['onebot_base_url']}")
    
    # 测试启动
    if bot.start():
        print("✓ 机器人启动成功")
    else:
        print("✗ 机器人启动失败（可能是OneBot服务未运行）")
        return False
    
    # 获取状态
    status = bot.get_status()
    print(f"✓ 机器人状态: 运行中={status['is_running']}, AI可用={status['ai_available']}")
    
    # 测试消息处理（模拟消息）
    test_message = {
        "message_type": "group",
        "group_id": "123456789",
        "user_id": "987654321",
        "message": f"[CQ:at,qq={Config.BOT_QQ}] 你好",
        "message_id": 12345
    }
    
    print("✓ 模拟消息处理测试（不会实际发送）")
    
    # 停止机器人
    bot.stop()
    print("✓ 机器人停止成功")
    
    return True

def test_api_connection():
    """测试API连接"""
    print("\n=== API连接测试 ===")
    
    try:
        client = LLOneBotClient(Config.ONEBOT_BASE_URL)
        
        # 测试获取状态
        result = client.get_status()
        if result.get("status") == "ok":
            print("✓ OneBot API连接正常")
            return True
        else:
            print(f"✗ OneBot API连接失败: {result}")
            return False
            
    except Exception as e:
        print(f"✗ OneBot API连接异常: {e}")
        return False

def test_ai_interface():
    """测试AI接口"""
    print("\n=== AI接口测试 ===")
    
    if not Config.KIMI_API_KEY:
        print("⚠ 未配置Kimi API Key，跳过AI测试")
        return True
    
    try:
        from ai_interface import KimiAI
        ai = KimiAI()
        
        # 测试连接
        if ai.test_connection():
            print("✓ Kimi AI连接正常")
            
            # 测试简单对话
            reply = ai.get_qq_reply("你好")
            if reply:
                print(f"✓ AI回复测试成功: {reply[:30]}...")
                return True
            else:
                print("✗ AI回复测试失败")
                return False
        else:
            print("✗ Kimi AI连接失败")
            return False
            
    except Exception as e:
        print(f"✗ AI接口测试异常: {e}")
        return False

def setup_environment():
    """设置测试环境"""
    print("=== 环境设置检查 ===")
    
    # 检查必要的环境变量
    required_vars = {
        "ONEBOT_BASE_URL": Config.ONEBOT_BASE_URL,
        "BOT_QQ": Config.BOT_QQ,
        "KIMI_API_KEY": Config.KIMI_API_KEY
    }
    
    missing_vars = []
    for var_name, var_value in required_vars.items():
        if not var_value:
            missing_vars.append(var_name)
        else:
            print(f"✓ {var_name}: {'*' * (len(var_value) - 4) + var_value[-4:] if len(var_value) > 4 else '已设置'}")
    
    if missing_vars:
        print(f"\n⚠ 缺少环境变量: {', '.join(missing_vars)}")
        print("请设置以下环境变量或在config.py中修改默认值：")
        for var in missing_vars:
            print(f"  export {var}=your_value")
        print()
    
    return len(missing_vars) == 0

def main():
    """主测试函数"""
    print("QQ机器人框架测试")
    print("=" * 50)
    
    # 环境检查
    env_ok = setup_environment()
    
    # 基本功能测试
    basic_ok = test_basic_functionality()
    
    # API连接测试
    api_ok = test_api_connection()
    
    # AI接口测试
    ai_ok = test_ai_interface()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"环境配置: {'✓' if env_ok else '✗'}")
    print(f"基本功能: {'✓' if basic_ok else '✗'}")
    print(f"API连接: {'✓' if api_ok else '✗'}")
    print(f"AI接口: {'✓' if ai_ok else '✗'}")
    
    if all([basic_ok, api_ok]):
        print("\n🎉 核心功能测试通过！机器人框架可以正常使用。")
        if not ai_ok:
            print("💡 提示: AI功能需要配置Kimi API Key才能使用。")
    else:
        print("\n❌ 部分测试失败，请检查配置和服务状态。")
    
    return all([basic_ok, api_ok])

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

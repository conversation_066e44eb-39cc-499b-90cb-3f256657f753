"""
QQ Bot 配置文件
"""
import os
from typing import Dict, Any

class Config:
    """配置管理类"""
    
    # OneBot API 配置
    ONEBOT_BASE_URL = os.getenv("ONEBOT_BASE_URL", "http://127.0.0.1:3000")
    
    # Kimi AI 配置
    KIMI_API_KEY = os.getenv("KIMI_API_KEY", "sk-Ft2FPysTY0I8mw35Tr8dzBBPBkjvSeCxBaEOhZNCs3k7eymg")
    KIMI_BASE_URL = os.getenv("KIMI_BASE_URL", "https://api.moonshot.cn/v1")
    KIMI_MODEL = os.getenv("KIMI_MODEL", "moonshot-v1-8k")
    
    # 机器人配置
    BOT_QQ = os.getenv("BOT_QQ", "3420659474")  # 机器人的QQ号
    
    # 人性化配置
    MIN_DELAY = 1.0  # 最小延迟（秒）
    MAX_DELAY = 3.0  # 最大延迟（秒）
    MESSAGE_QUERY_DELAY = 2.0  # 消息查询间隔
    
    # 消息处理配置
    MAX_MESSAGE_LENGTH = 500  # 最大消息长度
    ENABLE_AT_REPLY = True  # 是否启用@回复
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FILE = "qqbot.log"
    
    @classmethod
    def get_config(cls) -> Dict[str, Any]:
        """获取所有配置"""
        return {
            "onebot_base_url": cls.ONEBOT_BASE_URL,
            "kimi_api_key": cls.KIMI_API_KEY,
            "kimi_base_url": cls.KIMI_BASE_URL,
            "kimi_model": cls.KIMI_MODEL,
            "bot_qq": cls.BOT_QQ,
            "min_delay": cls.MIN_DELAY,
            "max_delay": cls.MAX_DELAY,
            "message_query_delay": cls.MESSAGE_QUERY_DELAY,
            "max_message_length": cls.MAX_MESSAGE_LENGTH,
            "enable_at_reply": cls.ENABLE_AT_REPLY,
            "log_level": cls.LOG_LEVEL,
            "log_file": cls.LOG_FILE,
        }
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置是否完整"""
        if not cls.KIMI_API_KEY:
            print("警告: KIMI_API_KEY 未设置")
            return False
        if not cls.BOT_QQ:
            print("警告: BOT_QQ 未设置")
            return False
        return True
